package de.sarocesch.sarosmoneymod.procedures;

import de.sarocesch.sarosmoneymod.init.SarosMoneyModModItems;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.Entity;
import net.minecraft.network.chat.Component;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.server.level.ServerPlayer;
import java.util.HashMap;
import java.util.Map;

public class WithdrawProcedure {
    public static void execute(Entity entity, HashMap guistate, String withdrawValue, ServerPlayer player) {
        if (entity == null || guistate == null)
            return;

        if (entity instanceof Player _player && !_player.level().isClientSide()) {
            try {
                int amount = Integer.parseInt(withdrawValue);
                if (amount > 0) {
                    String playerUUID = _player.getUUID().toString();

                    // Hole den aktuellen Kontostand über BalanceManager
                    double currentBalance = BalanceManager.getBalance(playerUUID);

                    if (amount <= currentBalance) {
                        Map<Integer, ItemStack> items = convertAmountToItems(amount);
                        boolean canFitAllItems = canFitItems(_player, items);

                        if (canFitAllItems) {
                            addItemsToPlayerInventory(_player, items);

                            double newBalance = currentBalance - amount;
                            // Aktualisiere den Kontostand über BalanceManager
                            BalanceManager.setBalance(playerUUID, newBalance);

                            _player.displayClientMessage(Component.literal("§9" + (Component.translatable("lang.withdraw.previous_balance").getString()) + " " + currentBalance), false);
                            _player.displayClientMessage(Component.literal("§a" + (Component.translatable("lang.withdraw.withdrawn_amount").getString()) + " " + amount), false);
                            _player.displayClientMessage(Component.literal("§6" + (Component.translatable("lang.withdraw.new_balance").getString()) + " " + newBalance), false);

                            // Leere das Eingabefeld
                            ((EditBox) guistate.get("text:withdraw")).setValue("");
                        } else {
                            _player.displayClientMessage(Component.literal("§c" + (Component.translatable("lang.withdraw.not_enough_inventory").getString())), false);
                        }
                    } else {
                        _player.displayClientMessage(Component.literal("§c" + (Component.translatable("lang.withdraw.not_enough_balance").getString())), false);
                    }
                } else {
                    _player.displayClientMessage(Component.literal("§c" + (Component.translatable("lang.withdraw.enter_positive_number").getString())), false);
                }
            } catch (NumberFormatException e) {
                _player.displayClientMessage(Component.literal("§c" + (Component.translatable("lang.withdraw.enter_valid_number").getString())), false);
            }
        }
    }

    private static Map<Integer, ItemStack> convertAmountToItems(int amount) {
        Map<Integer, ItemStack> items = new HashMap<>();
        int remaining = amount;

        int[] denominations = new int[]{500, 200, 100, 50, 20, 10, 5, 2, 1};
        ItemStack[] itemStacks = new ItemStack[]{
                new ItemStack(SarosMoneyModModItems.EURO_500.get()),
                new ItemStack(SarosMoneyModModItems.EURO_200.get()),
                new ItemStack(SarosMoneyModModItems.EURO_100.get()),
                new ItemStack(SarosMoneyModModItems.EURO_50.get()),
                new ItemStack(SarosMoneyModModItems.EURO_20.get()),
                new ItemStack(SarosMoneyModModItems.EURO_10.get()),
                new ItemStack(SarosMoneyModModItems.EURO_5.get()),
                new ItemStack(SarosMoneyModModItems.EURO_2.get()),
                new ItemStack(SarosMoneyModModItems.EURO_1.get())
        };

        for (int i = 0; i < denominations.length; i++) {
            int denom = denominations[i];
            int count = remaining / denom;
            if (count > 0) {
                ItemStack stack = itemStacks[i].copy();
                stack.setCount(count);
                items.put(denom, stack);
                remaining %= denom;
            }
        }

        return items;
    }

    private static boolean canFitItems(Player player, Map<Integer, ItemStack> items) {
        int freeSlots = 0;
        int slotsNeeded = 0;

        for (int i = 0; i < player.getInventory().items.size(); i++) {
            if (player.getInventory().items.get(i).isEmpty()) {
                freeSlots++;
            }
        }

        for (ItemStack stack : items.values()) {
            int remainingCount = stack.getCount();
            for (int i = 0; i < player.getInventory().items.size(); i++) {
                ItemStack invStack = player.getInventory().items.get(i);
                if (!invStack.isEmpty() && invStack.is(stack.getItem())) {
                    int spaceInStack = invStack.getMaxStackSize() - invStack.getCount();
                    remainingCount -= spaceInStack;
                    if (remainingCount <= 0) {
                        remainingCount = 0;
                        break;
                    }
                }
            }
            slotsNeeded += (remainingCount + stack.getMaxStackSize() - 1) / stack.getMaxStackSize();
        }

        return freeSlots >= slotsNeeded;
    }

    private static void addItemsToPlayerInventory(Player player, Map<Integer, ItemStack> items) {
        for (ItemStack stack : items.values()) {
            while (!stack.isEmpty()) {
                int remainingCount = stack.getCount();
                for (int i = 0; i < player.getInventory().items.size(); i++) {
                    ItemStack invStack = player.getInventory().items.get(i);
                    if (!invStack.isEmpty() && invStack.is(stack.getItem())) {
                        int spaceInStack = invStack.getMaxStackSize() - invStack.getCount();
                        if (remainingCount <= spaceInStack) {
                            invStack.grow(remainingCount);
                            stack.setCount(0);
                            break;
                        } else {
                            invStack.grow(spaceInStack);
                            remainingCount -= spaceInStack;
                        }
                    }
                }
                if (!stack.isEmpty()) {
                    player.addItem(stack.split(stack.getMaxStackSize()));
                }
            }
        }
    }
}

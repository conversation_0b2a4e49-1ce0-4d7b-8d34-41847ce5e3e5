package de.sarocesch.sarosmoneymod.init;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.block.entity.ATMBlockEntity;
import de.sarocesch.sarosmoneymod.block.entity.ATM2BlockEntity;
import de.sarocesch.sarosmoneymod.block.entity.ATM3BlockEntity;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.neoforged.neoforge.registries.DeferredRegister;

import java.util.function.Supplier;

public class SarosMoneyModModBlockEntities {
	public static final DeferredRegister<BlockEntityType<?>> REGISTRY =
			DeferredRegister.create(BuiltInRegistries.BLOCK_ENTITY_TYPE, SarosMoneyModMod.MODID);

	public static final DeferredHolder<BlockEntityType<?>, BlockEntityType<ATMBlockEntity>> ATM =
			register("atm", SarosMoneyModModBlocks.ATM, ATMBlockEntity::new);

	public static final DeferredHolder<BlockEntityType<?>, BlockEntityType<ATM2BlockEntity>> ATM_2 =
			register("atm_2", SarosMoneyModModBlocks.ATM_2, ATM2BlockEntity::new);

	public static final DeferredHolder<BlockEntityType<?>, BlockEntityType<ATM3BlockEntity>> ATM_3 =
			register("atm_3", SarosMoneyModModBlocks.ATM_3, ATM3BlockEntity::new);

	private static <T extends BlockEntityType<E>, E extends net.minecraft.world.level.block.entity.BlockEntity>
	DeferredHolder<BlockEntityType<?>, T> register(
			String registryname,
			Supplier<? extends net.minecraft.world.level.block.Block> block,
			BlockEntityType.BlockEntitySupplier<E> supplier) {

		return (DeferredHolder<BlockEntityType<?>, T>) REGISTRY.register(
				registryname,
				() -> BlockEntityType.Builder.of(supplier, block.get()).build(null)
		);
	}
}
package de.sarocesch.sarosmoneymod.init;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.item.*;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.block.Block;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.neoforged.neoforge.registries.DeferredRegister;

public class SarosMoneyModModItems {
	public static final DeferredRegister<Item> REGISTRY =
			DeferredRegister.create(BuiltInRegistries.ITEM, SarosMoneyModMod.MODID);

	// Euro Items
	public static final DeferredHolder<Item, Item> EURO_1 = REGISTRY.register("euro_1", Euro1Item::new);
	public static final DeferredHolder<Item, Item> EURO_2 = REGISTRY.register("euro_2", Euro2Item::new);
	public static final DeferredHolder<Item, Item> EURO_5 = REGISTRY.register("euro_5", Euro5Item::new);
	public static final DeferredHolder<Item, Item> EURO_10 = REGISTRY.register("euro_10", Euro10Item::new);
	public static final DeferredHolder<Item, Item> EURO_20 = REGISTRY.register("euro_20", Euro20Item::new);
	public static final DeferredHolder<Item, Item> EURO_50 = REGISTRY.register("euro_50", Euro50Item::new);
	public static final DeferredHolder<Item, Item> EURO_100 = REGISTRY.register("euro_100", Euro100Item::new);
	public static final DeferredHolder<Item, Item> EURO_200 = REGISTRY.register("euro_200", Euro200Item::new);
	public static final DeferredHolder<Item, Item> EURO_500 = REGISTRY.register("euro_500", Euro500Item::new);

	// Cent Items
	public static final DeferredHolder<Item, Item> CENT_1 = REGISTRY.register("cent_1", Cent1Item::new);
	public static final DeferredHolder<Item, Item> CENT_2 = REGISTRY.register("cent_2", Cent2Item::new);
	public static final DeferredHolder<Item, Item> CENT_5 = REGISTRY.register("cent_5", Cent5Item::new);
	public static final DeferredHolder<Item, Item> CENT_10 = REGISTRY.register("cent_10", Cent10Item::new);
	public static final DeferredHolder<Item, Item> CENT_20 = REGISTRY.register("cent_20", Cent20Item::new);
	public static final DeferredHolder<Item, Item> CENT_50 = REGISTRY.register("cent_50", Cent50Item::new);

	// Block Items
	public static final DeferredHolder<Item, Item> ATM = block(SarosMoneyModModBlocks.ATM);
	public static final DeferredHolder<Item, Item> ATM_2 = block(SarosMoneyModModBlocks.ATM_2);
	public static final DeferredHolder<Item, Item> ATM_3 = block(SarosMoneyModModBlocks.ATM_3);
	public static final DeferredHolder<Item, Item> BANKER = block(SarosMoneyModModBlocks.BANKER);

	// Wallet Items
	public static final DeferredHolder<Item, Item> WALLET = REGISTRY.register("wallet", WalletItem::new);
	public static final DeferredHolder<Item, Item> WALLET_BLACK = REGISTRY.register("wallet_black", BlackWalletItem::new);
	public static final DeferredHolder<Item, Item> WALLET_BLUE = REGISTRY.register("wallet_blue", BlueWalletItem::new);
	public static final DeferredHolder<Item, Item> WALLET_BROWN = REGISTRY.register("wallet_brown", BrownWalletItem::new);
	public static final DeferredHolder<Item, Item> WALLET_GREEN = REGISTRY.register("wallet_green", GreenWalletItem::new);
	public static final DeferredHolder<Item, Item> WALLET_ORANGE = REGISTRY.register("wallet_orange", OrangeWalletItem::new);
	public static final DeferredHolder<Item, Item> WALLET_PURPLE = REGISTRY.register("wallet_purple", PurpleWalletItem::new);
	public static final DeferredHolder<Item, Item> WALLET_YELLOW = REGISTRY.register("wallet_yellow", YellowWalletItem::new);

	private static DeferredHolder<Item, Item> block(DeferredHolder<Block, Block> block) {
		return REGISTRY.register(block.getId().getPath(),
				() -> new BlockItem(block.get(), new Item.Properties()));
	}
}
package de.sarocesch.sarosmoneymod.procedures;

import de.sarocesch.sarosmoneymod.init.SarosMoneyModModItems;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.Entity;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import java.util.function.Supplier;
import java.util.Map;
import java.util.HashMap;

public class DepositProcedure {
    public static void execute(Entity entity, HashMap guistate, String depositValue, ServerPlayer player) {
        if (entity == null)
            return;

        double totalAmountToAdd = 0.0;
        // Hole den ItemStack aus dem Slot (überprüft, ob entity ein Player ist und ein ContainerMenu besitzt)
        ItemStack stack = (entity instanceof Player _plrSlotItem
                && _plrSlotItem.containerMenu instanceof Supplier _splr
                && _splr.get() instanceof Map _slt
                ? ((Slot) _slt.get(0)).getItem()
                : ItemStack.EMPTY);

        if (!stack.isEmpty()) {
            int itemCount = stack.getCount();

            if (stack.getItem() == SarosMoneyModModItems.EURO_1.get()) {
                totalAmountToAdd = 1.0 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.EURO_2.get()) {
                totalAmountToAdd = 2.0 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.EURO_5.get()) {
                totalAmountToAdd = 5.0 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.EURO_10.get()) {
                totalAmountToAdd = 10.0 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.EURO_20.get()) {
                totalAmountToAdd = 20.0 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.EURO_50.get()) {
                totalAmountToAdd = 50.0 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.EURO_100.get()) {
                totalAmountToAdd = 100.0 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.EURO_200.get()) {
                totalAmountToAdd = 200.0 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.EURO_500.get()) {
                totalAmountToAdd = 500.0 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.CENT_1.get()) {
                // 1 Cent = 0.01 Euro
                totalAmountToAdd = 0.01 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.CENT_2.get()) {
                totalAmountToAdd = 0.02 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.CENT_5.get()) {
                totalAmountToAdd = 0.05 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.CENT_10.get()) {
                totalAmountToAdd = 0.10 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.CENT_20.get()) {
                totalAmountToAdd = 0.20 * itemCount;
            } else if (stack.getItem() == SarosMoneyModModItems.CENT_50.get()) {
                totalAmountToAdd = 0.50 * itemCount;
            }

            if (totalAmountToAdd > 0 && entity instanceof Player _player) {
                if (!_player.level().isClientSide()) {
                    String playerUUID = _player.getUUID().toString();

                    // Lese den bisherigen Kontostand über den BalanceManager
                    double previousBalance = BalanceManager.getBalance(playerUUID);
                    double newBalance = previousBalance + totalAmountToAdd;

                    // Aktualisiere den Kontostand über den BalanceManager
                    BalanceManager.setBalance(playerUUID, newBalance);

                    _player.displayClientMessage(Component.literal("§9"
                            + Component.translatable("lang.deposit.previous_balance").getString()
                            + " " + String.format("%.2f", previousBalance)), false);
                    _player.displayClientMessage(Component.literal("§a"
                            + Component.translatable("lang.deposit.added_amount").getString()
                            + " " + String.format("%.2f", totalAmountToAdd)), false);
                    _player.displayClientMessage(Component.literal("§6"
                            + Component.translatable("lang.deposit.new_balance").getString()
                            + " " + String.format("%.2f", newBalance)), false);

                    // Leere den Slot und aktualisiere das ContainerMenu
                    if (_player.containerMenu instanceof AbstractContainerMenu) {
                        AbstractContainerMenu menu = (AbstractContainerMenu) _player.containerMenu;
                        Slot slot = menu.getSlot(0);
                        slot.set(ItemStack.EMPTY);
                        menu.broadcastChanges();
                    }
                }
            }
        }
    }
}

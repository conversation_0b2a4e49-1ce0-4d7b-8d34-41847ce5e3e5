package de.sarocesch.sarosmoneymod.listener;

import de.sarocesch.sarosmoneymod.Config;
import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.neoforged.neoforge.event.entity.living.LivingDeathEvent;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.EventBusSubscriber;

@EventBusSubscriber(modid = SarosMoneyModMod.MODID)
public class DeathMoneyListener {

    @SubscribeEvent
    public static void onPlayerDeath(LivingDeathEvent event) {
        if (!(event.getEntity() instanceof ServerPlayer player)) return;

        // Hole den Betrag, der bei Tod verloren geht, aus der Konfiguration
        int deathMoneyLoss = Config.MONEY_LOSS_ON_DEATH;

        // Falls kein Verlust definiert ist, wird nichts getan
        if (deathMoneyLoss == 0) return;

        String playerUUID = player.getUUID().toString();

        // Hole den aktuellen Kontostand über BalanceManager
        double balance = BalanceManager.getBalance(playerUUID);

        // Berechne den neuen Kontostand (sorge dafür, dass dieser nicht negativ wird)
        double newBalance = Math.max(balance - deathMoneyLoss, 0);

        // Aktualisiere den Kontostand direkt über BalanceManager
        BalanceManager.setBalance(playerUUID, newBalance);

        // Sende eine Nachricht an den Spieler
        player.sendSystemMessage(Component.translatable("message.death_money_loss", deathMoneyLoss, newBalance));
    }
}
package de.sarocesch.sarosmoneymod.client.gui;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import net.minecraft.world.level.Level;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.network.chat.Component;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import de.sarocesch.sarosmoneymod.world.inventory.ATMGUIMenu;
import de.sarocesch.sarosmoneymod.network.NetworkHandler;
import net.minecraft.core.BlockPos;

import java.util.HashMap;

import com.mojang.blaze3d.systems.RenderSystem;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.components.Button;
import de.sarocesch.sarosmoneymod.network.ATMGUIButtonMessage;

public class ATMGUIScreen extends AbstractContainerScreen<ATMGUIMenu> {
    private static final HashMap<String, Object> guistate = ATMGUIMenu.guistate;
    private final Level world;
    private final int x, y, z;
    private final Player entity;
    private EditBox withdraw;
    private Button button_payout;
    private Button button_deposit;
    private int lastButtonClickTick;
    private static final int UPDATE_DELAY_TICKS = 5;
    private int tickCounter;

    private int glowTimer = 0;
    private double prevBalance = -1;

    public ATMGUIScreen(ATMGUIMenu container, Inventory inventory, Component text) {
        super(container, inventory, text);
        this.world = container.world;
        this.x = container.x;
        this.y = container.y;
        this.z = container.z;
        this.entity = container.entity;
        this.imageWidth = 176;
        this.imageHeight = 166;

        this.tickCounter = 0;
        this.lastButtonClickTick = -1;
    }

    private static final ResourceLocation texture = ResourceLocation.fromNamespaceAndPath(
        SarosMoneyModMod.MODID,
        "textures/screens/atmgui.png"
    );

    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTicks) {
        this.renderBackground(guiGraphics, mouseX, mouseY, partialTicks);
        super.render(guiGraphics, mouseX, mouseY, partialTicks);
        withdraw.render(guiGraphics, mouseX, mouseY, partialTicks);
        this.renderTooltip(guiGraphics, mouseX, mouseY);
    }

    @Override
    protected void renderBg(GuiGraphics guiGraphics, float partialTicks, int gx, int gy) {
        RenderSystem.setShaderColor(1, 1, 1, 1);
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        guiGraphics.blit(texture, this.leftPos, this.topPos, 0, 0, this.imageWidth, this.imageHeight, this.imageWidth, this.imageHeight);
        RenderSystem.disableBlend();
    }

    @Override
    public boolean keyPressed(int key, int b, int c) {
        if (key == 256) {
            this.minecraft.player.closeContainer();
            return true;
        }
        if (withdraw.isFocused())
            return withdraw.keyPressed(key, b, c);
        return super.keyPressed(key, b, c);
    }

    @Override
    protected void renderLabels(GuiGraphics guiGraphics, int mouseX, int mouseY) {
        double balance = menu.getBalance();
        String balanceText = "Balance: " + String.format("%.2f", balance);
        int normalColor = -12829636;

        if (glowTimer > 0) {
            float progress = glowTimer / 20.0F;
            int alpha = (int) (progress * 255);
            int glowColor = (alpha << 24) | 0xFFFFFF;

            for (int offsetX = -1; offsetX <= 1; offsetX++) {
                for (int offsetY = -1; offsetY <= 1; offsetY++) {
                    if (offsetX != 0 || offsetY != 0) {
                        guiGraphics.drawString(
                                font,
                                balanceText,
                                3 + offsetX,
                                16 + offsetY,
                                glowColor,
                                false
                        );
                    }
                }
            }
        }

        guiGraphics.drawString(font, balanceText, 3, 16, normalColor, false);
        guiGraphics.drawString(font, Component.translatable("ATM"), 3, 4, normalColor, false);
    }

    @Override
    public void containerTick() {
        super.containerTick();
        // Remove the withdraw.tick() call - it's no longer needed
        tickCounter++;

        if (lastButtonClickTick != -1 && tickCounter - lastButtonClickTick >= UPDATE_DELAY_TICKS) {
            lastButtonClickTick = -1;
        }

        double currentBalance = menu.getBalance();
        if (Math.abs(currentBalance - prevBalance) > 0.001) {
            glowTimer = 20;
            prevBalance = currentBalance;
        }
        if (glowTimer > 0) {
            glowTimer--;
        }
    }

    @Override
    public void onClose() {
        super.onClose();
    }

    @Override
    public void init() {
        super.init();
        withdraw = new EditBox(
                this.font,
                this.leftPos + 6,
                this.topPos + 39,
                54,
                20,
                Component.translatable("gui.saros__money_mod.atmgui.withdraw")
        );
        withdraw.setMaxLength(32767);
        guistate.put("text:withdraw", withdraw);
        this.addWidget(withdraw);

        button_payout = Button.builder(
                        Component.translatable("gui.saros__money_mod.atmgui.button_payout"),
                        e -> {
                            String value = withdraw.getValue();
                            NetworkHandler.sendToServer(
                                new ATMGUIButtonMessage(0, BlockPos.ZERO, value)
                            );
                            withdraw.setValue("");
                            lastButtonClickTick = tickCounter;
                        })
                .pos(this.leftPos + 112, this.topPos + 39)
                .size(56, 20)
                .build();

        guistate.put("button:button_payout", button_payout);
        this.addRenderableWidget(button_payout);
        withdraw.setValue("");

        button_deposit = Button.builder(
                        Component.translatable("gui.saros__money_mod.atmgui.button_deposit"),
                        e -> {
                            String value = withdraw.getValue();
                            NetworkHandler.sendToServer(
                                new ATMGUIButtonMessage(1, BlockPos.ZERO, value)
                            );
                            withdraw.setValue("");
                            lastButtonClickTick = tickCounter;
                        })
                .pos(this.leftPos + 112, this.topPos + 62)
                .size(56, 20)
                .build();

        guistate.put("button:button_deposit", button_deposit);
        this.addRenderableWidget(button_deposit);
    }
}
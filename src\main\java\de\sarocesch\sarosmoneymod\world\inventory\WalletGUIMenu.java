package de.sarocesch.sarosmoneymod.world.inventory;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModMenus;
import net.neoforged.neoforge.items.SlotItemHandler;
import net.neoforged.neoforge.items.ItemStackHandler;
import net.neoforged.neoforge.items.IItemHandler;
import net.neoforged.neoforge.capabilities.Capabilities;

import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.Entity;
import net.minecraft.tags.ItemTags;
import net.minecraft.tags.TagKey;
import net.minecraft.world.item.Item;
import net.minecraft.nbt.CompoundTag;
import de.sarocesch.sarosmoneymod.item.WalletItem;
import de.sarocesch.sarosmoneymod.item.BlackWalletItem;
import de.sarocesch.sarosmoneymod.item.BlueWalletItem;
import de.sarocesch.sarosmoneymod.item.BrownWalletItem;
import de.sarocesch.sarosmoneymod.item.GreenWalletItem;
import de.sarocesch.sarosmoneymod.item.OrangeWalletItem;
import de.sarocesch.sarosmoneymod.item.PurpleWalletItem;
import de.sarocesch.sarosmoneymod.item.YellowWalletItem;
import net.minecraft.core.component.DataComponents;
// import net.minecraft.world.item.component.ContainerComponent; // Nicht in Forge 1.21 verfügbar
import net.minecraft.world.item.component.CustomData;
import net.minecraft.world.Container;
// import de.sarocesch.sarosmoneymod.item.inventory.WalletInventoryCapability; // Nicht mehr benötigt in Forge 1.21

import net.minecraft.server.level.ServerPlayer;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.core.BlockPos;
import net.minecraft.core.RegistryAccess;

import java.util.function.Supplier;
import java.util.Map;
import java.util.HashMap;

public class WalletGUIMenu extends AbstractContainerMenu implements Supplier<Map<Integer, Slot>> {
	public final static HashMap<String, Object> guistate = new HashMap<>();
	public final Level world;
	public final Player entity;
	//public final int x, y, z;
	/// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	private IItemHandler internal;
	private final Map<Integer, Slot> customSlots = new HashMap<>();
	private boolean bound = false;

	private static final TagKey<Item> MONEY_TAG = ItemTags.create(ResourceLocation.parse("saros__money_mod:sarosmoney"));

	public WalletGUIMenu(int id, Inventory inv, FriendlyByteBuf extraData) {
		super(SarosMoneyModModMenus.WALLET_GUI.get(), id);
		this.entity = inv.player;
		this.world = inv.player.level();
		this.internal = new ItemStackHandler(27);
		BlockPos pos = null;
		if (extraData != null) {
			pos = extraData.readBlockPos();
			//this.x = pos.getX();
			//this.y = pos.getY();
			//this.z = pos.getZ();
			/// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
			int hand = extraData.readInt();
			if (hand == 0) {
				ItemStack itemstack;
				if (hand == 0)
					itemstack = this.entity.getMainHandItem();
				else
					itemstack = this.entity.getOffhandItem();

				// Hole die CustomData aus dem ItemStack
				CustomData customData = itemstack.get(DataComponents.CUSTOM_DATA);
				if (customData != null) {
					CompoundTag nbt = customData.copyTag();
					if (nbt.contains("Inventory")) {
						CompoundTag inventoryTag = nbt.getCompound("Inventory");
						this.internal = new ItemStackHandler(27); // Standardgröße für Wallet

						// Load items manually from the custom format
						if (inventoryTag.contains("Items")) {
							CompoundTag itemsTag = inventoryTag.getCompound("Items");
							for (String key : itemsTag.getAllKeys()) {
								if (key.startsWith("slot_")) {
									int slotIndex = Integer.parseInt(key.substring(5));
									CompoundTag itemTag = itemsTag.getCompound(key);
									ItemStack itemStack = ItemStack.parse(entity.level().registryAccess(), itemTag).orElse(ItemStack.EMPTY);
									((ItemStackHandler)this.internal).setStackInSlot(slotIndex, itemStack);
								}
							}
						}
						this.bound = true;

						// Wallet-Referenz setzen (angepasst für Data Components)
						if (this.internal instanceof ItemStackHandler handler) {
							SarosMoneyModMod.LOGGER.info("Wallet-Inventar geladen");
						}
					}
				}
			} else if (extraData.readableBytes() > 1) {
				extraData.readByte(); // drop padding
				Entity entity = world.getEntity(extraData.readVarInt());
				if (entity != null) {
					//if (entity instanceof Player player) {
						// Spieler-Inventar laden
					//	CustomData customData = player.get(DataComponents.CUSTOM_DATA);
					//	if (customData != null) {
					//		CompoundTag nbt = customData.copyTag();
					//		if (nbt.contains("Inventory")) {
					//			CompoundTag inventoryTag = nbt.getCompound("Inventory");
					//			this.internal = new ItemStackHandler(18);
								// In Forge 1.21, die deserializeNBT-Methode erwartet zwei Parameter
								// Wir müssen einen Workaround verwenden
					//			try {
									// Versuche, die Methode mit Reflection aufzurufen
					//				java.lang.reflect.Method method = ((ItemStackHandler)this.internal).getClass().getMethod("deserializeNBT", CompoundTag.class);
					//				method.invoke(((ItemStackHandler)this.internal), inventoryTag);
					//			} catch (Exception e) {
									// Fallback: Manuelles Laden der Items
					//				if (inventoryTag.contains("Items")) {
					//					CompoundTag itemsTag = inventoryTag.getCompound("Items");
					//					for (String key : itemsTag.getAllKeys()) {
					//						if (key.startsWith("slot_")) {
					//							int slotIndex = Integer.parseInt(key.substring(5));
					//							CompoundTag itemTag = itemsTag.getCompound(key);
												//ItemStack itemStack = ItemStack.parse(itemTag);
												//((ItemStackHandler)this.internal).setStackInSlot(slotIndex, itemStack);
												/// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
					//						}
					//					}
					//				}
					//			}
					//			this.bound = true;
					//		}
					//	}
					//}
				}
			} else { // might be bound to block
				BlockEntity ent = inv.player != null ? inv.player.level().getBlockEntity(pos) : null;
				if (ent != null) {
					// BlockEntity-Handling für Data Components
					if (ent instanceof Container container) {
						this.internal = new ItemStackHandler(container.getContainerSize());
						for (int i = 0; i < container.getContainerSize(); i++) {
							((ItemStackHandler)this.internal).setStackInSlot(i, container.getItem(i));
						}
						this.bound = true;
					}
				}
			}
		}
		// Create wallet slots in 3 rows of 9 slots each (27 total)
		for (int row = 0; row < 3; row++) {
			for (int col = 0; col < 9; col++) {
				int slotIndex = row * 9 + col;
				int x = 8 + col * 18;
				int y = 38 + row * 18; // Start at Y=38 for the first row, then 56, 74 (moved down 12 pixels)

				this.customSlots.put(slotIndex, this.addSlot(new SlotItemHandler(internal, slotIndex, x, y) {
					@Override
					public boolean mayPlace(ItemStack stack) {
						return stack.is(MONEY_TAG);
					}
				}));
			}
		}
		// Player inventory (3 rows) - moved down to accommodate 3 wallet rows (adjusted for 12px lower wallet slots)
		for (int si = 0; si < 3; ++si)
			for (int sj = 0; sj < 9; ++sj)
				this.addSlot(new Slot(inv, sj + (si + 1) * 9, 0 + 8 + sj * 18, 0 + 114 + si * 18));
		// Player hotbar - moved down accordingly
		for (int si = 0; si < 9; ++si)
			this.addSlot(new Slot(inv, si, 0 + 8 + si * 18, 0 + 172));
	}

	@Override
	public boolean stillValid(Player player) {
		return true;
	}

	@Override
	public ItemStack quickMoveStack(Player playerIn, int index) {
		ItemStack itemstack = ItemStack.EMPTY;
		Slot slot = (Slot) this.slots.get(index);
		if (slot != null && slot.hasItem()) {
			ItemStack itemstack1 = slot.getItem();
			itemstack = itemstack1.copy();
			if (index < 27) {
				if (!this.moveItemStackTo(itemstack1, 27, this.slots.size(), true)) {
					return ItemStack.EMPTY;
				}
				slot.onQuickCraft(itemstack1, itemstack);
			} else if (!this.moveItemStackTo(itemstack1, 0, 27, false)) {
				if (index < 27 + 27) {
					if (!this.moveItemStackTo(itemstack1, 27 + 27, this.slots.size(), true)) {
						return ItemStack.EMPTY;
					}
				} else {
					if (!this.moveItemStackTo(itemstack1, 27, 27 + 27, false)) {
						return ItemStack.EMPTY;
					}
				}
				return ItemStack.EMPTY;
			}
			if (itemstack1.getCount() == 0) {
				slot.set(ItemStack.EMPTY);
			} else {
				slot.setChanged();
			}
			if (itemstack1.getCount() == itemstack.getCount()) {
				return ItemStack.EMPTY;
			}
			slot.onTake(playerIn, itemstack1);
		}
		return itemstack;
	}

	@Override
	protected boolean moveItemStackTo(ItemStack p_38904_, int p_38905_, int p_38906_, boolean p_38907_) {
		boolean flag = false;
		int i = p_38905_;
		if (p_38907_) {
			i = p_38906_ - 1;
		}
		if (p_38904_.isStackable()) {
			while (!p_38904_.isEmpty()) {
				if (p_38907_) {
					if (i < p_38905_) {
						break;
					}
				} else if (i >= p_38906_) {
					break;
				}
				Slot slot = this.slots.get(i);
				ItemStack itemstack = slot.getItem();
				if (slot.mayPlace(p_38904_) && !itemstack.isEmpty() && ItemStack.isSameItem(p_38904_, itemstack)) {
					int j = itemstack.getCount() + p_38904_.getCount();
					int maxSize = Math.min(slot.getMaxStackSize(), p_38904_.getMaxStackSize());
					if (j <= maxSize) {
						p_38904_.setCount(0);
						itemstack.setCount(j);
						slot.set(itemstack);
						flag = true;
					} else if (itemstack.getCount() < maxSize) {
						p_38904_.shrink(maxSize - itemstack.getCount());
						itemstack.setCount(maxSize);
						slot.set(itemstack);
						flag = true;
					}
				}
				if (p_38907_) {
					--i;
				} else {
					++i;
				}
			}
		}
		if (!p_38904_.isEmpty()) {
			if (p_38907_) {
				i = p_38906_ - 1;
			} else {
				i = p_38905_;
			}
			while (true) {
				if (p_38907_) {
					if (i < p_38905_) {
						break;
					}
				} else if (i >= p_38906_) {
					break;
				}
				Slot slot1 = this.slots.get(i);
				ItemStack itemstack1 = slot1.getItem();
				if (itemstack1.isEmpty() && slot1.mayPlace(p_38904_)) {
					if (p_38904_.getCount() > slot1.getMaxStackSize()) {
						slot1.set(p_38904_.split(slot1.getMaxStackSize()));
					} else {
						slot1.set(p_38904_.split(p_38904_.getCount()));
					}
					slot1.setChanged();
					flag = true;
					break;
				}
				if (p_38907_) {
					--i;
				} else {
					++i;
				}
			}
		}
		return flag;
	}

	@Override
	public void removed(Player playerIn) {
		super.removed(playerIn);
		if (!bound && playerIn instanceof ServerPlayer serverPlayer) {
			if (!serverPlayer.isAlive() || serverPlayer.hasDisconnected()) {
				for (int j = 0; j < internal.getSlots(); ++j) {
					playerIn.drop(internal.extractItem(j, internal.getStackInSlot(j).getCount(), false), false);
				}
			} else {
				for (int i = 0; i < internal.getSlots(); ++i) {
					playerIn.getInventory().placeItemBackInInventory(internal.extractItem(i, internal.getStackInSlot(i).getCount(), false));
				}
			}
		} else {
			// Wenn der Spieler das GUI schließt, speichere den Inhalt im Wallet-Item
			if (playerIn instanceof ServerPlayer serverPlayer) {
				// Suche das Wallet-Item im Inventar des Spielers
				ItemStack[] walletStackRef = new ItemStack[1];
				int[] walletSlotRef = new int[1];

				// Durchsuche das Inventar nach dem Wallet-Item
				for (int i = 0; i < serverPlayer.getInventory().getContainerSize(); i++) {
					ItemStack stack = serverPlayer.getInventory().getItem(i);
					if (stack.getItem() instanceof WalletItem ||
						stack.getItem() instanceof BlackWalletItem ||
						stack.getItem() instanceof BlueWalletItem ||
						stack.getItem() instanceof BrownWalletItem ||
						stack.getItem() instanceof GreenWalletItem ||
						stack.getItem() instanceof OrangeWalletItem ||
						stack.getItem() instanceof PurpleWalletItem ||
						stack.getItem() instanceof YellowWalletItem) {
						walletStackRef[0] = stack;
						walletSlotRef[0] = i;
						break;
					}
				}

				// If we found the wallet, save the inventory to it
				if (walletStackRef[0] != null) {
					final ItemStack walletStack = walletStackRef[0]; // Lokale finale Kopie für Lambda

					try {
						// Erstelle einen temporären Handler für das GUI-Inventar
						ItemStackHandler tempHandler = new ItemStackHandler(internal.getSlots());

						// Kopiere die Items aus dem GUI-Inventar in den temporären Handler
						for (int i = 0; i < internal.getSlots(); i++) {
							ItemStack slotStack = internal.getStackInSlot(i);
							if (!slotStack.isEmpty()) {
								SarosMoneyModMod.LOGGER.info("GUI item in slot " + i + ": " + slotStack.getItem().getClass().getSimpleName() + " x" + slotStack.getCount());
							}
							tempHandler.setStackInSlot(i, slotStack.copy());
						}

						// Check if the tempHandler has items
						SarosMoneyModMod.LOGGER.info("Checking temporary handler");
						for (int i = 0; i < tempHandler.getSlots(); i++) {
							ItemStack slotStack = tempHandler.getStackInSlot(i);
							if (!slotStack.isEmpty()) {
								SarosMoneyModMod.LOGGER.info("Temporary handler item in slot " + i + ": " + slotStack.getItem().getClass().getSimpleName() + " x" + slotStack.getCount());
							}
						}

						// Verwende CustomData für Wallet-Inventar
						CustomData customData = walletStack.get(DataComponents.CUSTOM_DATA);
						if (customData != null) {
							CompoundTag nbt = customData.copyTag();
							if (nbt.contains("Inventory")) {
								CompoundTag inventoryTag = nbt.getCompound("Inventory");
								// Erstelle einen temporären Handler für das Wallet-Inventar
								ItemStackHandler walletHandler = new ItemStackHandler(18);
								// Lade die Items aus dem inventoryTag
								if (inventoryTag.contains("Items")) {
									CompoundTag itemsTag = inventoryTag.getCompound("Items");
									for (String key : itemsTag.getAllKeys()) {
										if (key.startsWith("slot_")) {
											int slotIndex = Integer.parseInt(key.substring(5));
											CompoundTag itemTag = itemsTag.getCompound(key);
											ItemStack itemStack = ItemStack.parse(entity.level().registryAccess(), itemTag).orElse(ItemStack.EMPTY);
											walletHandler.setStackInSlot(slotIndex, itemStack);
										}
									}
								}
							}
						}

						// Aktualisiere das Wallet-Inventar mit CustomData
						// Erstelle einen neuen Handler für das Wallet
						ItemStackHandler walletHandler2 = new ItemStackHandler(18);
						// Copy the items from the temporary handler to the wallet handler
						SarosMoneyModMod.LOGGER.info("Copying items to wallet handler");
						for (int i = 0; i < tempHandler.getSlots(); i++) {
							ItemStack slotStack = tempHandler.getStackInSlot(i);
							if (!slotStack.isEmpty()) {
								SarosMoneyModMod.LOGGER.info("Copying item in slot " + i + ": " + slotStack.getItem().getClass().getSimpleName() + " x" + slotStack.getCount());
							}
							walletHandler2.setStackInSlot(i, slotStack);
						}

						// Check if the wallet handler has items after copying
						SarosMoneyModMod.LOGGER.info("Checking wallet handler after copying");
						for (int i = 0; i < walletHandler2.getSlots(); i++) {
							ItemStack slotStack = walletHandler2.getStackInSlot(i);
							if (!slotStack.isEmpty()) {
								SarosMoneyModMod.LOGGER.info("Wallet handler item in slot " + i + ": " + slotStack.getItem().getClass().getSimpleName() + " x" + slotStack.getCount());
							}
						}

						// Speichere den aktualisierten Handler in CustomData
						// Erstelle einen neuen CompoundTag für die Inventardaten
						CompoundTag inventoryTag = new CompoundTag();
						CompoundTag itemsTag = new CompoundTag();

						// Speichere jedes Item einzeln
						for (int i = 0; i < walletHandler2.getSlots(); i++) {
							ItemStack slotStack = walletHandler2.getStackInSlot(i);
							if (!slotStack.isEmpty()) {
								CompoundTag itemTag = new CompoundTag();
								slotStack.save(entity.level().registryAccess(), itemTag);
								itemsTag.put("slot_" + i, itemTag);
							}
						}

						inventoryTag.put("Items", itemsTag);

						// Hole die aktuelle CustomData oder erstelle eine neue
						CompoundTag nbt = walletStack.getOrDefault(DataComponents.CUSTOM_DATA, CustomData.EMPTY).copyTag();

						// Füge die Inventardaten hinzu
						nbt.put("Inventory", inventoryTag);

						// Erstelle eine neue CustomData-Instanz mit dem aktualisierten Tag
						CustomData newCustomData = CustomData.of(nbt);

						// Setze die neue CustomData-Instanz im Wallet-ItemStack
						walletStack.set(DataComponents.CUSTOM_DATA, newCustomData);

						// Save the inventory to the wallet item
						try {
							SarosMoneyModMod.LOGGER.info("Speichere Inventar in Wallet beim Schließen der GUI");

							// Erstelle einen neuen CompoundTag für die Inventardaten
							CompoundTag inventoryTag2 = new CompoundTag();

							// Speichere die Items direkt als einzelne Tags
							CompoundTag itemsTag2 = new CompoundTag();
							int itemCount = 0;

							// Sammle alle Items aus den GUI-Slots
							for (int i = 0; i < internal.getSlots(); i++) {
								ItemStack slotStack = internal.getStackInSlot(i);
								if (!slotStack.isEmpty()) {
									// Erstelle einen Tag für dieses Item
									CompoundTag itemTag = new CompoundTag();
									slotStack.save(entity.level().registryAccess(), itemTag);

									// Füge den Tag zum Items-Tag hinzu
									itemsTag2.put("slot_" + i, itemTag);
									SarosMoneyModMod.LOGGER.info("Item in Slot " + i + " gespeichert: " + slotStack.getItem().getClass().getSimpleName() + " x" + slotStack.getCount());
									itemCount++;
								}
							}

							SarosMoneyModMod.LOGGER.info("Insgesamt " + itemCount + " Items gespeichert");
							inventoryTag2.put("Items", itemsTag2);
							SarosMoneyModMod.LOGGER.info("Inventardaten erstellt");

							// Erstelle einen neuen CustomData-Tag
							CompoundTag nbt2 = new CompoundTag();

							// Füge die Inventardaten hinzu
							nbt2.put("Inventory", inventoryTag2);

							// Erstelle eine neue CustomData-Instanz mit dem aktualisierten Tag
							CustomData newCustomData2 = CustomData.of(nbt2);

							// Setze die neue CustomData-Instanz im Wallet-ItemStack
							walletStack.set(DataComponents.CUSTOM_DATA, newCustomData2);
							SarosMoneyModMod.LOGGER.info("CustomData gesetzt");

							// Wir haben die Daten bereits in CustomData gespeichert, keine weitere Aktion notwendig

							// Überprüfe, ob die Daten korrekt gespeichert wurden
							CustomData checkData = walletStack.get(DataComponents.CUSTOM_DATA);
							if (checkData != null) {
								SarosMoneyModMod.LOGGER.info("CustomData nach dem Setzen: " + checkData);
								if (checkData.copyTag().contains("Inventory")) {
									SarosMoneyModMod.LOGGER.info("Inventory wurde erfolgreich gespeichert!");

									// Aktualisiere den Client
									if (serverPlayer.containerMenu != null) {
										serverPlayer.containerMenu.broadcastChanges();
										SarosMoneyModMod.LOGGER.info("Inventaränderungen an den Client gesendet");
									}
								}
							}
						} catch (Exception e) {
							SarosMoneyModMod.LOGGER.error("Fehler beim Aktualisieren des Wallet-Stacks: " + e.getMessage());
							e.printStackTrace();
						}
					} catch (Exception e) {
						SarosMoneyModMod.LOGGER.error("Fehler beim Aktualisieren des Wallet-Stacks: " + e.getMessage());
						e.printStackTrace();
					}
				} else {
					// If we couldn't find the wallet, drop the items
					for (int i = 0; i < internal.getSlots(); ++i) {
						playerIn.getInventory().placeItemBackInInventory(internal.extractItem(i, internal.getStackInSlot(i).getCount(), false));
					}
				}
			}
		}
	}

	@Override
	public Map<Integer, Slot> get() {
		return customSlots;
	}
}

package de.sarocesch.sarosmoneymod.world.inventory;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModMenus;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModItems;
import net.neoforged.neoforge.items.SlotItemHandler;
import net.neoforged.neoforge.items.ItemStackHandler;
import net.neoforged.neoforge.items.IItemHandler;
import net.neoforged.neoforge.capabilities.Capabilities;

import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.Entity;
import net.minecraft.tags.ItemTags;
import net.minecraft.tags.TagKey;
import net.minecraft.world.item.Item;
import net.minecraft.nbt.CompoundTag;
import de.sarocesch.sarosmoneymod.item.WalletItem;
import de.sarocesch.sarosmoneymod.item.BlackWalletItem;
import de.sarocesch.sarosmoneymod.item.BlueWalletItem;
import de.sarocesch.sarosmoneymod.item.BrownWalletItem;
import de.sarocesch.sarosmoneymod.item.GreenWalletItem;
import de.sarocesch.sarosmoneymod.item.OrangeWalletItem;
import de.sarocesch.sarosmoneymod.item.PurpleWalletItem;
import de.sarocesch.sarosmoneymod.item.YellowWalletItem;
import net.minecraft.core.component.DataComponents;
// import net.minecraft.world.item.component.ContainerComponent; // Nicht in Forge 1.21 verfügbar
import net.minecraft.world.item.component.CustomData;
import net.minecraft.world.Container;
// import de.sarocesch.sarosmoneymod.item.inventory.WalletInventoryCapability; // Nicht mehr benötigt in Forge 1.21

import net.minecraft.server.level.ServerPlayer;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.core.BlockPos;
import net.minecraft.core.RegistryAccess;

import java.util.function.Supplier;
import java.util.Map;
import java.util.HashMap;

public class WalletGUIMenu extends AbstractContainerMenu implements Supplier<Map<Integer, Slot>> {
	public final static HashMap<String, Object> guistate = new HashMap<>();
	public final Level world;
	public final Player entity;
	//public final int x, y, z;
	/// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	private IItemHandler internal;
	private final Map<Integer, Slot> customSlots = new HashMap<>();
	private boolean bound = false;

	private static final TagKey<Item> MONEY_TAG = ItemTags.create(ResourceLocation.parse("saros__money_mod:sarosmoney"));

	// Helper method to check if an item is a money item (alternative to tag system)
	private static boolean isMoneyItem(ItemStack stack) {
		Item item = stack.getItem();
		return item == SarosMoneyModModItems.EURO_1.get() ||
			   item == SarosMoneyModModItems.EURO_2.get() ||
			   item == SarosMoneyModModItems.EURO_5.get() ||
			   item == SarosMoneyModModItems.EURO_10.get() ||
			   item == SarosMoneyModModItems.EURO_20.get() ||
			   item == SarosMoneyModModItems.EURO_50.get() ||
			   item == SarosMoneyModModItems.EURO_100.get() ||
			   item == SarosMoneyModModItems.EURO_200.get() ||
			   item == SarosMoneyModModItems.EURO_500.get() ||
			   item == SarosMoneyModModItems.CENT_1.get() ||
			   item == SarosMoneyModModItems.CENT_2.get() ||
			   item == SarosMoneyModModItems.CENT_5.get() ||
			   item == SarosMoneyModModItems.CENT_10.get() ||
			   item == SarosMoneyModModItems.CENT_20.get() ||
			   item == SarosMoneyModModItems.CENT_50.get();
	}

	public WalletGUIMenu(int id, Inventory inv, FriendlyByteBuf extraData) {
		super(SarosMoneyModModMenus.WALLET_GUI.get(), id);
		this.entity = inv.player;
		this.world = inv.player.level();
		this.internal = new ItemStackHandler(27);
		BlockPos pos = null;
		if (extraData != null) {
			pos = extraData.readBlockPos();
			//this.x = pos.getX();
			//this.y = pos.getY();
			//this.z = pos.getZ();
			/// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
			int hand = extraData.readInt();
			if (hand == 0) {
				ItemStack itemstack;
				if (hand == 0)
					itemstack = this.entity.getMainHandItem();
				else
					itemstack = this.entity.getOffhandItem();

				// Hole die CustomData aus dem ItemStack
				SarosMoneyModMod.LOGGER.info("=== STARTING WALLET LOAD PROCESS ===");
				SarosMoneyModMod.LOGGER.info("Loading wallet: " + itemstack.getItem().getClass().getSimpleName());

				CustomData customData = itemstack.get(DataComponents.CUSTOM_DATA);
				if (customData != null) {
					CompoundTag nbt = customData.copyTag();
					SarosMoneyModMod.LOGGER.info("Found CustomData in wallet: " + nbt.toString());

					if (nbt.contains("Inventory")) {
						CompoundTag inventoryTag = nbt.getCompound("Inventory");
						SarosMoneyModMod.LOGGER.info("Found Inventory tag: " + inventoryTag.toString());

						this.internal = new ItemStackHandler(27); // Standardgröße für Wallet
						SarosMoneyModMod.LOGGER.info("Created ItemStackHandler with 27 slots");

						// Load items manually from the custom format
						if (inventoryTag.contains("Items")) {
							CompoundTag itemsTag = inventoryTag.getCompound("Items");
							SarosMoneyModMod.LOGGER.info("Found Items tag with " + itemsTag.getAllKeys().size() + " entries");

							int loadedItems = 0;
							for (String key : itemsTag.getAllKeys()) {
								if (key.startsWith("slot_")) {
									int slotIndex = Integer.parseInt(key.substring(5));
									CompoundTag itemTag = itemsTag.getCompound(key);
									SarosMoneyModMod.LOGGER.info("Loading item from key: " + key + " (slot " + slotIndex + ")");
									SarosMoneyModMod.LOGGER.info("Item NBT: " + itemTag.toString());

									ItemStack itemStack = ItemStack.parse(entity.level().registryAccess(), itemTag).orElse(ItemStack.EMPTY);
									if (!itemStack.isEmpty()) {
										((ItemStackHandler)this.internal).setStackInSlot(slotIndex, itemStack);
										SarosMoneyModMod.LOGGER.info("Loaded item into slot " + slotIndex + ": " + itemStack.getItem().getClass().getSimpleName() + " x" + itemStack.getCount());
										loadedItems++;
									} else {
										SarosMoneyModMod.LOGGER.warn("Failed to parse item from NBT for slot " + slotIndex);
									}
								}
							}
							SarosMoneyModMod.LOGGER.info("Total items loaded: " + loadedItems);
						} else {
							SarosMoneyModMod.LOGGER.info("No Items tag found in inventory");
						}
						this.bound = true;

						// Verify loaded items
						SarosMoneyModMod.LOGGER.info("=== VERIFYING LOADED ITEMS ===");
						for (int i = 0; i < this.internal.getSlots(); i++) {
							ItemStack slotStack = this.internal.getStackInSlot(i);
							if (!slotStack.isEmpty()) {
								SarosMoneyModMod.LOGGER.info("Verified slot " + i + ": " + slotStack.getItem().getClass().getSimpleName() + " x" + slotStack.getCount());
							}
						}

						// Wallet-Referenz setzen (angepasst für Data Components)
						if (this.internal instanceof ItemStackHandler handler) {
							SarosMoneyModMod.LOGGER.info("=== WALLET LOAD PROCESS COMPLETED ===");
						}
					} else {
						SarosMoneyModMod.LOGGER.info("No Inventory tag found in CustomData");
					}
				} else {
					SarosMoneyModMod.LOGGER.info("No CustomData found in wallet item");
				}
			} else if (extraData.readableBytes() > 1) {
				extraData.readByte(); // drop padding
				Entity entity = world.getEntity(extraData.readVarInt());
				if (entity != null) {
					//if (entity instanceof Player player) {
						// Spieler-Inventar laden
					//	CustomData customData = player.get(DataComponents.CUSTOM_DATA);
					//	if (customData != null) {
					//		CompoundTag nbt = customData.copyTag();
					//		if (nbt.contains("Inventory")) {
					//			CompoundTag inventoryTag = nbt.getCompound("Inventory");
					//			this.internal = new ItemStackHandler(18);
								// In Forge 1.21, die deserializeNBT-Methode erwartet zwei Parameter
								// Wir müssen einen Workaround verwenden
					//			try {
									// Versuche, die Methode mit Reflection aufzurufen
					//				java.lang.reflect.Method method = ((ItemStackHandler)this.internal).getClass().getMethod("deserializeNBT", CompoundTag.class);
					//				method.invoke(((ItemStackHandler)this.internal), inventoryTag);
					//			} catch (Exception e) {
									// Fallback: Manuelles Laden der Items
					//				if (inventoryTag.contains("Items")) {
					//					CompoundTag itemsTag = inventoryTag.getCompound("Items");
					//					for (String key : itemsTag.getAllKeys()) {
					//						if (key.startsWith("slot_")) {
					//							int slotIndex = Integer.parseInt(key.substring(5));
					//							CompoundTag itemTag = itemsTag.getCompound(key);
												//ItemStack itemStack = ItemStack.parse(itemTag);
												//((ItemStackHandler)this.internal).setStackInSlot(slotIndex, itemStack);
												/// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
					//						}
					//					}
					//				}
					//			}
					//			this.bound = true;
					//		}
					//	}
					//}
				}
			} else { // might be bound to block
				BlockEntity ent = inv.player != null ? inv.player.level().getBlockEntity(pos) : null;
				if (ent != null) {
					// BlockEntity-Handling für Data Components
					if (ent instanceof Container container) {
						this.internal = new ItemStackHandler(container.getContainerSize());
						for (int i = 0; i < container.getContainerSize(); i++) {
							((ItemStackHandler)this.internal).setStackInSlot(i, container.getItem(i));
						}
						this.bound = true;
					}
				}
			}
		}
		// Create wallet slots in 3 rows of 9 slots each (27 total)
		for (int row = 0; row < 3; row++) {
			for (int col = 0; col < 9; col++) {
				int slotIndex = row * 9 + col;
				int x = 8 + col * 18;
				int y = 18 + row * 18; // Start at Y=18 for the first row, then 36, 54 (standard GUI positioning)

				this.customSlots.put(slotIndex, this.addSlot(new SlotItemHandler(internal, slotIndex, x, y) {
					@Override
					public boolean mayPlace(ItemStack stack) {
						// Try tag-based filtering first, then fall back to direct item checking
						return stack.is(MONEY_TAG) || isMoneyItem(stack);
					}
				}));
			}
		}
		// Player inventory (3 rows) - positioned below wallet slots with proper spacing
		for (int si = 0; si < 3; ++si)
			for (int sj = 0; sj < 9; ++sj)
				this.addSlot(new Slot(inv, sj + (si + 1) * 9, 0 + 8 + sj * 18, 0 + 84 + si * 18));
		// Player hotbar - positioned below player inventory
		for (int si = 0; si < 9; ++si)
			this.addSlot(new Slot(inv, si, 0 + 8 + si * 18, 0 + 142));
	}

	@Override
	public boolean stillValid(Player player) {
		return true;
	}

	@Override
	public ItemStack quickMoveStack(Player playerIn, int index) {
		ItemStack itemstack = ItemStack.EMPTY;
		Slot slot = (Slot) this.slots.get(index);
		if (slot != null && slot.hasItem()) {
			ItemStack itemstack1 = slot.getItem();
			itemstack = itemstack1.copy();
			if (index < 27) {
				if (!this.moveItemStackTo(itemstack1, 27, this.slots.size(), true)) {
					return ItemStack.EMPTY;
				}
				slot.onQuickCraft(itemstack1, itemstack);
			} else if (!this.moveItemStackTo(itemstack1, 0, 27, false)) {
				if (index < 27 + 27) {
					if (!this.moveItemStackTo(itemstack1, 27 + 27, this.slots.size(), true)) {
						return ItemStack.EMPTY;
					}
				} else {
					if (!this.moveItemStackTo(itemstack1, 27, 27 + 27, false)) {
						return ItemStack.EMPTY;
					}
				}
				return ItemStack.EMPTY;
			}
			if (itemstack1.getCount() == 0) {
				slot.set(ItemStack.EMPTY);
			} else {
				slot.setChanged();
			}
			if (itemstack1.getCount() == itemstack.getCount()) {
				return ItemStack.EMPTY;
			}
			slot.onTake(playerIn, itemstack1);
		}
		return itemstack;
	}

	@Override
	protected boolean moveItemStackTo(ItemStack p_38904_, int p_38905_, int p_38906_, boolean p_38907_) {
		boolean flag = false;
		int i = p_38905_;
		if (p_38907_) {
			i = p_38906_ - 1;
		}
		if (p_38904_.isStackable()) {
			while (!p_38904_.isEmpty()) {
				if (p_38907_) {
					if (i < p_38905_) {
						break;
					}
				} else if (i >= p_38906_) {
					break;
				}
				Slot slot = this.slots.get(i);
				ItemStack itemstack = slot.getItem();
				if (slot.mayPlace(p_38904_) && !itemstack.isEmpty() && ItemStack.isSameItem(p_38904_, itemstack)) {
					int j = itemstack.getCount() + p_38904_.getCount();
					int maxSize = Math.min(slot.getMaxStackSize(), p_38904_.getMaxStackSize());
					if (j <= maxSize) {
						p_38904_.setCount(0);
						itemstack.setCount(j);
						slot.set(itemstack);
						flag = true;
					} else if (itemstack.getCount() < maxSize) {
						p_38904_.shrink(maxSize - itemstack.getCount());
						itemstack.setCount(maxSize);
						slot.set(itemstack);
						flag = true;
					}
				}
				if (p_38907_) {
					--i;
				} else {
					++i;
				}
			}
		}
		if (!p_38904_.isEmpty()) {
			if (p_38907_) {
				i = p_38906_ - 1;
			} else {
				i = p_38905_;
			}
			while (true) {
				if (p_38907_) {
					if (i < p_38905_) {
						break;
					}
				} else if (i >= p_38906_) {
					break;
				}
				Slot slot1 = this.slots.get(i);
				ItemStack itemstack1 = slot1.getItem();
				if (itemstack1.isEmpty() && slot1.mayPlace(p_38904_)) {
					if (p_38904_.getCount() > slot1.getMaxStackSize()) {
						slot1.set(p_38904_.split(slot1.getMaxStackSize()));
					} else {
						slot1.set(p_38904_.split(p_38904_.getCount()));
					}
					slot1.setChanged();
					flag = true;
					break;
				}
				if (p_38907_) {
					--i;
				} else {
					++i;
				}
			}
		}
		return flag;
	}

	@Override
	public void removed(Player playerIn) {
		SarosMoneyModMod.LOGGER.info("=== WALLET GUI REMOVED/CLOSED ===");
		SarosMoneyModMod.LOGGER.info("Player: " + playerIn.getName().getString());
		SarosMoneyModMod.LOGGER.info("Bound: " + bound);
		SarosMoneyModMod.LOGGER.info("Is ServerPlayer: " + (playerIn instanceof ServerPlayer));

		super.removed(playerIn);
		if (!bound && playerIn instanceof ServerPlayer serverPlayer) {
			SarosMoneyModMod.LOGGER.info("Not bound - handling item drops/returns");
			if (!serverPlayer.isAlive() || serverPlayer.hasDisconnected()) {
				SarosMoneyModMod.LOGGER.info("Player dead/disconnected - dropping items");
				for (int j = 0; j < internal.getSlots(); ++j) {
					ItemStack slotStack = internal.getStackInSlot(j);
					if (!slotStack.isEmpty()) {
						SarosMoneyModMod.LOGGER.info("Dropping item from slot " + j + ": " + slotStack.getItem().getClass().getSimpleName());
					}
					playerIn.drop(internal.extractItem(j, internal.getStackInSlot(j).getCount(), false), false);
				}
			} else {
				SarosMoneyModMod.LOGGER.info("Player alive - returning items to inventory");
				for (int i = 0; i < internal.getSlots(); ++i) {
					ItemStack slotStack = internal.getStackInSlot(i);
					if (!slotStack.isEmpty()) {
						SarosMoneyModMod.LOGGER.info("Returning item from slot " + i + ": " + slotStack.getItem().getClass().getSimpleName());
					}
					playerIn.getInventory().placeItemBackInInventory(internal.extractItem(i, internal.getStackInSlot(i).getCount(), false));
				}
			}
		} else {
			// When the player closes the GUI, save the content to the wallet item
			SarosMoneyModMod.LOGGER.info("Bound wallet - saving inventory to wallet item");
			if (playerIn instanceof ServerPlayer serverPlayer) {
				saveWalletInventory(serverPlayer);
			}
		}
		SarosMoneyModMod.LOGGER.info("=== WALLET GUI REMOVAL COMPLETED ===");
	}

	// Helper method to check if an item is a wallet item
	private boolean isWalletItem(ItemStack stack) {
		return stack.getItem() instanceof WalletItem ||
			   stack.getItem() instanceof BlackWalletItem ||
			   stack.getItem() instanceof BlueWalletItem ||
			   stack.getItem() instanceof BrownWalletItem ||
			   stack.getItem() instanceof GreenWalletItem ||
			   stack.getItem() instanceof OrangeWalletItem ||
			   stack.getItem() instanceof PurpleWalletItem ||
			   stack.getItem() instanceof YellowWalletItem;
	}

	// Helper method to save wallet inventory
	private void saveWalletInventory(ServerPlayer serverPlayer) {
		SarosMoneyModMod.LOGGER.info("=== STARTING WALLET SAVE PROCESS ===");
		try {
			// Find the wallet in the player's inventory
			ItemStack walletStack = null;
			int walletSlot = -1;

			SarosMoneyModMod.LOGGER.info("Searching for wallet in player inventory...");
			for (int i = 0; i < serverPlayer.getInventory().getContainerSize(); i++) {
				ItemStack stack = serverPlayer.getInventory().getItem(i);
				if (isWalletItem(stack)) {
					walletStack = stack;
					walletSlot = i;
					SarosMoneyModMod.LOGGER.info("Found wallet at slot " + i + ": " + stack.getItem().getClass().getSimpleName());
					break;
				}
			}

			if (walletStack != null) {
				SarosMoneyModMod.LOGGER.info("=== WALLET FOUND - PROCEEDING WITH SAVE ===");
				SarosMoneyModMod.LOGGER.info("Wallet item: " + walletStack.getItem().getClass().getSimpleName());
				SarosMoneyModMod.LOGGER.info("Internal handler slots: " + internal.getSlots());

				// Log current wallet NBT data before saving
				CustomData existingData = walletStack.get(DataComponents.CUSTOM_DATA);
				if (existingData != null) {
					SarosMoneyModMod.LOGGER.info("Existing wallet NBT: " + existingData.copyTag().toString());
				} else {
					SarosMoneyModMod.LOGGER.info("No existing NBT data in wallet");
				}

				// Log all items currently in the GUI slots
				SarosMoneyModMod.LOGGER.info("=== CURRENT GUI SLOT CONTENTS ===");
				int totalItems = 0;
				for (int i = 0; i < internal.getSlots(); i++) {
					ItemStack slotStack = internal.getStackInSlot(i);
					if (!slotStack.isEmpty()) {
						SarosMoneyModMod.LOGGER.info("GUI Slot " + i + ": " + slotStack.getItem().getClass().getSimpleName() + " x" + slotStack.getCount());
						totalItems++;
					}
				}
				SarosMoneyModMod.LOGGER.info("Total items in GUI: " + totalItems);

				// Create the inventory data structure
				CompoundTag inventoryTag = new CompoundTag();
				CompoundTag itemsTag = new CompoundTag();
				inventoryTag.putInt("Size", 27); // Set correct size

				int itemCount = 0;
				// Save each item from the GUI slots
				SarosMoneyModMod.LOGGER.info("=== SAVING ITEMS TO NBT ===");
				for (int i = 0; i < internal.getSlots(); i++) {
					ItemStack slotStack = internal.getStackInSlot(i);
					if (!slotStack.isEmpty()) {
						CompoundTag itemTag = new CompoundTag();
						slotStack.save(serverPlayer.level().registryAccess(), itemTag);
						itemsTag.put("slot_" + i, itemTag);
						SarosMoneyModMod.LOGGER.info("Saved to NBT - Slot " + i + ": " + slotStack.getItem().getClass().getSimpleName() + " x" + slotStack.getCount());
						SarosMoneyModMod.LOGGER.info("Item NBT: " + itemTag.toString());
						itemCount++;
					}
				}

				inventoryTag.put("Items", itemsTag);
				SarosMoneyModMod.LOGGER.info("Total items saved to NBT: " + itemCount);
				SarosMoneyModMod.LOGGER.info("Complete inventory NBT: " + inventoryTag.toString());

				// Create the main NBT structure
				CompoundTag nbt = walletStack.getOrDefault(DataComponents.CUSTOM_DATA, CustomData.EMPTY).copyTag();
				nbt.put("Inventory", inventoryTag);
				SarosMoneyModMod.LOGGER.info("Final NBT structure: " + nbt.toString());

				// Save to the wallet item
				CustomData newCustomData = CustomData.of(nbt);
				walletStack.set(DataComponents.CUSTOM_DATA, newCustomData);

				// Verify the data was saved
				CustomData verifyData = walletStack.get(DataComponents.CUSTOM_DATA);
				if (verifyData != null) {
					CompoundTag verifyNbt = verifyData.copyTag();
					SarosMoneyModMod.LOGGER.info("=== VERIFICATION: Data saved successfully ===");
					SarosMoneyModMod.LOGGER.info("Verified NBT: " + verifyNbt.toString());

					if (verifyNbt.contains("Inventory")) {
						CompoundTag verifyInventory = verifyNbt.getCompound("Inventory");
						if (verifyInventory.contains("Items")) {
							CompoundTag verifyItems = verifyInventory.getCompound("Items");
							SarosMoneyModMod.LOGGER.info("Verified items count: " + verifyItems.getAllKeys().size());
						}
					}
				} else {
					SarosMoneyModMod.LOGGER.error("VERIFICATION FAILED: No data found after saving!");
				}

				SarosMoneyModMod.LOGGER.info("=== WALLET SAVE PROCESS COMPLETED ===");
			} else {
				// If we couldn't find the wallet, drop the items
				SarosMoneyModMod.LOGGER.error("=== WALLET NOT FOUND - DROPPING ITEMS ===");
				for (int i = 0; i < internal.getSlots(); ++i) {
					ItemStack slotStack = internal.getStackInSlot(i);
					if (!slotStack.isEmpty()) {
						SarosMoneyModMod.LOGGER.warn("Dropping item from slot " + i + ": " + slotStack.getItem().getClass().getSimpleName());
					}
					serverPlayer.getInventory().placeItemBackInInventory(internal.extractItem(i, internal.getStackInSlot(i).getCount(), false));
				}
			}
		} catch (Exception e) {
			SarosMoneyModMod.LOGGER.error("=== ERROR IN WALLET SAVE PROCESS ===");
			SarosMoneyModMod.LOGGER.error("Error saving wallet inventory: " + e.getMessage());
			e.printStackTrace();
		}
	}

	@Override
	public Map<Integer, Slot> get() {
		return customSlots;
	}
}

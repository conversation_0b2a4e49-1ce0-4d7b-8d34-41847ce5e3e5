package de.sarocesch.sarosmoneymod.data;

import net.minecraft.core.HolderLookup;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.level.saveddata.SavedData;
import net.minecraft.util.datafix.DataFixTypes;

import java.util.HashMap;
import java.util.Map;

public class BalanceData extends SavedData {
    private static final String DATA_NAME = "sarosmoneymod_balance";
    // Speichert für jeden Spieler (UUID als String) den Kontostand
    private final Map<String, Double> balances = new HashMap<>();

    public BalanceData() {
    }

    /**
     * Factory für die SavedData-Klasse
     */
    public static Factory<BalanceData> factory() {
        return new Factory<>(
            () -> new BalanceData(),
            (tag, provider) -> load(tag, provider),
            null // DataFixTypes.LEVEL für Daten-Konvertierung, null wenn nicht benötigt
        );
    }

    /**
     * Lese-Methode: Erstellt ein neues BalanceData aus einem CompoundTag.
     */
    public static BalanceData load(CompoundTag compound, HolderLookup.Provider provider) {
        BalanceData data = new BalanceData();
        if (compound.contains("balances", 10)) { // 10 = Compound-Tag
            CompoundTag mapTag = compound.getCompound("balances");
            for (String key : mapTag.getAllKeys()) {
                // Unterstützung für alte Integer-Werte und neue Double-Werte
                if (mapTag.contains(key, 6)) { // 6 = Double-Tag
                    data.balances.put(key, mapTag.getDouble(key));
                } else {
                    // Konvertiere alte Integer-Werte zu Double
                    data.balances.put(key, (double) mapTag.getInt(key));
                }
            }
        }
        return data;
    }

    /**
     * Speichert die Daten in ein CompoundTag.
     */
    @Override
    public CompoundTag save(CompoundTag p_77763_, HolderLookup.Provider p_334349_) {
        CompoundTag mapTag = new CompoundTag();
        for (Map.Entry<String, Double> entry : balances.entrySet()) {
            mapTag.putDouble(entry.getKey(), entry.getValue());
        }
        p_77763_.put("balances", mapTag);
        return p_77763_;
    }

    /**
     * Liefert den Kontostand für die angegebene Spieler-UUID.
     * Ist kein Eintrag vorhanden, wird 0 zurückgegeben.
     */
    public double getBalance(String playerUUID) {
        return balances.getOrDefault(playerUUID, 0.0);
    }

    /**
     * Prüft, ob für den Spieler bereits ein Eintrag existiert.
     */
    public boolean containsBalance(String playerUUID) {
        return balances.containsKey(playerUUID);
    }

    /**
     * Setzt den Kontostand für die angegebene Spieler-UUID und markiert die Daten als geändert.
     */
    public void setBalance(String playerUUID, double balance) {
        balances.put(playerUUID, balance);
        this.setDirty();
    }

    /**
     * Liefert eine Kopie der internen Balances-Map.
     * Achtung: Es wird eine neue Map zurückgegeben, um direkte Modifikationen an den internen Daten zu vermeiden.
     */
    public Map<String, Double> getAllBalances() {
        return new HashMap<>(this.balances);
    }

    /**
     * Holt die BalanceData-Instanz aus dem Overworld-Datencontainer.
     * Wir prüfen hier, ob der übergebene Server nicht null ist.
     */
    public static BalanceData get(MinecraftServer server) {
        if (server == null) {
            throw new IllegalArgumentException("Server instance is null");
        }
        ServerLevel level = server.overworld();
        return level.getDataStorage().computeIfAbsent(
            factory(),
            DATA_NAME
        );
    }
}

package de.sarocesch.sarosmoneymod.procedures;

import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.block.Block;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModBlocks;
import net.minecraft.world.item.Item;

public class ATM2BlockAddedProcedure {
    public static void execute(LevelAccessor world, double x, double y, double z) {
        // Konvertierung von double zu int
        BlockPos lowerBlockPos = new BlockPos((int)x, (int)y, (int)z);
        BlockPos upperBlockPos = new BlockPos((int)x, (int)(y + 1), (int)z);

        // Den aktuellen Zustand des unteren Blocks abrufen
        BlockState lowerBlockState = world.getBlockState(lowerBlockPos);

        // Überprüfen, ob der Block oberhalb nicht AIR ist
        if (world.getBlockState(upperBlockPos).is(Blocks.AIR)) {
            // Der Block oberhalb ist AIR, also Block setzen

            // Den neuen Blockzustand erstellen
            BlockState newBlockState = SarosMoneyModModBlocks.ATM_2.get().defaultBlockState();

            // Beispiel für die Rotationseigenschaft
            if (lowerBlockState.hasProperty(BlockStateProperties.HORIZONTAL_FACING)) {
                newBlockState = newBlockState.setValue(BlockStateProperties.HORIZONTAL_FACING, lowerBlockState.getValue(BlockStateProperties.HORIZONTAL_FACING));
            }

            // Setzen des neuen Blocks
            world.setBlock(upperBlockPos, newBlockState, 3);
        } else {
            // Der Block oberhalb ist nicht AIR, daher den neuen Block nicht setzen

            // Droppen des Blocks
            dropBlock(world, lowerBlockPos, SarosMoneyModModBlocks.ATM_2.get().asItem());
        }

        // Entfernen des unteren Blocks
        world.setBlock(lowerBlockPos, Blocks.AIR.defaultBlockState(), 3);
    }

    private static void dropBlock(LevelAccessor world, BlockPos pos, Item item) {
        if (world instanceof Level) {
            Level level = (Level) world;
            BlockState state = level.getBlockState(pos);
            Block.dropResources(state, level, pos, null, null, new ItemStack(item));
        }
    }
}

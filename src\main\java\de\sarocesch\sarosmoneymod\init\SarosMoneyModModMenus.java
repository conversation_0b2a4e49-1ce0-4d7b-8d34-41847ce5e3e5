package de.sarocesch.sarosmoneymod.init;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.world.inventory.ATMGUIMenu;
import de.sarocesch.sarosmoneymod.world.inventory.WalletGUIMenu;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.world.inventory.MenuType;
import net.neoforged.neoforge.common.extensions.IMenuTypeExtension;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.neoforged.neoforge.registries.DeferredRegister;

public class SarosMoneyModModMenus {
	public static final DeferredRegister<MenuType<?>> REGISTRY =
			DeferredRegister.create(BuiltInRegistries.MENU, SarosMoneyModMod.MODID);

	public static final DeferredHolder<MenuType<?>, MenuType<ATMGUIMenu>> ATMGUI =
			REGISTRY.register("atmgui", () -> IMenuTypeExtension.create(ATMGUIMenu::new));

	public static final DeferredHolder<MenuType<?>, MenuType<WalletGUIMenu>> WALLET_GUI =
			REGISTRY.register("wallet_gui", () -> IMenuTypeExtension.create(WalletGUIMenu::new));
}
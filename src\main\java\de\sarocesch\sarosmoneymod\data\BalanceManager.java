package de.sarocesch.sarosmoneymod.data;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.neoforged.neoforge.event.entity.player.PlayerEvent;
import net.neoforged.neoforge.event.server.ServerStartedEvent;
import net.neoforged.bus.api.SubscribeEvent;
import de.sarocesch.sarosmoneymod.Config;
import java.util.Map;

public class BalanceManager {

    /**
     * Fügt dem Spieler über das SavedData-System einen bestimmten Betrag hinzu.
     */
    public static void addMoneyToPlayer(MinecraftServer server, String playerUUID, double amount) {
        double current = BalanceManager.getBalance(playerUUID);
        BalanceManager.setBalance(playerUUID, current + amount);
    }

    /**
     * Gibt alle Spieler-G<PERSON>aben als Kopie der internen Map zurück.
     */
    public static Map<String, Double> loadBalances(MinecraftServer server) {
        return BalanceData.get(server).getAllBalances();
    }

    // Schnittstelle zur Nutzung in Commands:

    /**
     * Liest den Kontostand des Spielers.
     *
     * @param player der ServerPlayer
     * @return aktueller Kontostand
     */
    public static double getPlayerBalance(ServerPlayer player) {
        return BalanceManager.getBalance(player.getUUID().toString());
    }

    /**
     * Aktualisiert den Kontostand des Spielers.
     *
     * @param player der ServerPlayer
     * @param newBalance der neue Kontostand
     */
    public static void updatePlayerBalance(ServerPlayer player, double newBalance) {
        BalanceManager.setBalance(player.getUUID().toString(), newBalance);
    }


    // Static server reference for convenience methods
    private static MinecraftServer currentServer;

    /**
     * Event handler for server startup - automatically sets the server instance.
     */
    @SubscribeEvent
    public static void onServerStarted(ServerStartedEvent event) {
        currentServer = event.getServer();
    }

    /**
     * Event handler for player login - gives starting balance to new players.
     */
    @SubscribeEvent
    public static void onPlayerLogin(PlayerEvent.PlayerLoggedInEvent event) {
        if (event.getEntity() instanceof ServerPlayer player) {
            String uuid = player.getUUID().toString();
            if (getBalance(uuid) == 0.0) {
                setBalance(uuid, Config.START_MONEY); // Use configurable starting balance
            }
        }
    }

    /**
     * Sets the current server instance. Should be called during server startup.
     */
    public static void setCurrentServer(MinecraftServer server) {
        currentServer = server;
    }

    /**
     * Gets the current server instance.
     */
    public static MinecraftServer getCurrentServer() {
        return currentServer;
    }

    // Liefert den aktuellen Kontostand des Spielers
    public static double getBalance(String playerUUID) {
        MinecraftServer server = getCurrentServer();
        if (server == null) {
            return 0.0;
        }
        BalanceData data = BalanceData.get(server);
        return data.getBalance(playerUUID);
    }

    // Aktualisiert den Kontostand des Spielers und markiert die Daten als "dirty"
    public static void setBalance(String playerUUID, double balance) {
        MinecraftServer server = getCurrentServer();
        if (server == null) {
            return;
        }
        BalanceData data = BalanceData.get(server);
        data.setBalance(playerUUID, balance);
    }

}

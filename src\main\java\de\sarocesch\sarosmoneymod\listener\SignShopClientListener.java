package de.sarocesch.sarosmoneymod.listener;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.world.level.block.entity.SignBlockEntity;
import net.minecraft.world.level.block.entity.SignText;
import net.neoforged.api.distmarker.Dist;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.neoforge.client.event.ScreenEvent;

import java.lang.reflect.Field;

import static de.sarocesch.sarosmoneymod.SarosMoneyModMod.LOGGER;

@EventBusSubscriber(modid = SarosMoneyModMod.MODID, value = Dist.CLIENT)
public class SignShopClientListener {

    private static Field signField;

    private static void initSignField() {
        try {
            // Use standard Java reflection instead of ReflectionHelper
            signField = AbstractSignEditScreen.class.getDeclaredField("sign");
            signField.setAccessible(true);
        } catch (NoSuchFieldException e) {
            // Try alternative field names if "sign" doesn't work
            try {
                signField = AbstractSignEditScreen.class.getDeclaredField("blockEntity");
                signField.setAccessible(true);
            } catch (NoSuchFieldException e2) {
                SarosMoneyModMod.LOGGER.error("Failed to find sign field with both 'sign' and 'blockEntity' names:", e2);
            }
        } catch (Exception e) {
            SarosMoneyModMod.LOGGER.error("Failed to find sign field:", e);
        }
    }

    @SubscribeEvent
    public static void onScreenInit(ScreenEvent.Init.Pre event) {
        if (event.getScreen() instanceof AbstractSignEditScreen) {
            if (signField == null) {
                initSignField();
            }
        }
    }

    @SubscribeEvent
    public static void onScreenClose(ScreenEvent.Closing event) {
        if (event.getScreen() instanceof AbstractSignEditScreen screen) {
            try {
                if (signField == null) {
                    initSignField();
                }

                SignBlockEntity sign = (SignBlockEntity) signField.get(screen);
                if (sign != null) {
                    SignText signText = sign.getText(true);
                    Component[] messages = signText.getMessages(true);

                    // Check if this is a shop sign
                    String firstLine = messages[0].getString().trim().toLowerCase();
                    boolean isShop = firstLine.contains("shop") ||
                            firstLine.contains("buy") ||
                            firstLine.contains("sell");

                    if (isShop) {
                        LOGGER.debug("Detected shop sign creation, sending to server");
                    }

                    // Schedule sign update on main thread
                    Minecraft.getInstance().tell(() -> updateSign(sign));
                }
            } catch (Exception e) {
                LOGGER.error("Error processing sign edit screen closing:", e);
            }
        }
    }

    private static void updateSign(SignBlockEntity sign) {
        try {
            sign.setChanged();
            var level = sign.getLevel();
            if (level != null) {
                level.sendBlockUpdated(
                        sign.getBlockPos(),
                        level.getBlockState(sign.getBlockPos()),
                        level.getBlockState(sign.getBlockPos()),
                        3
                );
            }
            LOGGER.info("Sign updated at " + sign.getBlockPos());
        } catch (Exception e) {
            LOGGER.error("Sign update failed", e);
        }
    }
}
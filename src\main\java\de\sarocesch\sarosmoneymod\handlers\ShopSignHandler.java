package de.sarocesch.sarosmoneymod.handlers;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.Container;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.ChestBlockEntity;
import net.minecraft.world.level.block.state.BlockState;

import java.util.UUID;

public class ShopSignHandler {

    private static String stripFormatting(String text) {
        return text.replaceAll("§[0-9a-fk-or]", "").trim();
    }

    public static void handleSignInteraction(UUID ownerUUID, Player customer, String itemName, double price, String type, BlockPos signPos) {
        // Debug logging removed

        if (!(customer instanceof ServerPlayer serverCustomer)) {
            // Debug logging removed
            return;
        }

        Level level = customer.level();
        BlockState signState = level.getBlockState(signPos);
        BlockPos containerPos = ContainerHandler.getAttachedContainerPos(level, signPos, signState);
        BlockEntity blockEntity = level.getBlockEntity(containerPos);

        if (!(blockEntity instanceof Container container)) {
            customer.sendSystemMessage(Component.translatable("shop.error.no_container")
                    .withStyle(ChatFormatting.RED));
            return;
        }

        // Synchronisiere Container-Status
        if (blockEntity instanceof ChestBlockEntity chest) {
            chest.setChanged();
        }

        if (type.equalsIgnoreCase("buy")) {
            handleBuyInteraction(ownerUUID, serverCustomer, itemName, price, container);
        } else if (type.equalsIgnoreCase("sell")) {
            handleSellInteraction(ownerUUID, serverCustomer, itemName, price, container);
        }

        // Erzwinge Inventar-Synchronisation
        serverCustomer.inventoryMenu.broadcastChanges();
        serverCustomer.containerMenu.sendAllDataToRemote();
    }

    private static void handleBuyInteraction(UUID ownerUUID, ServerPlayer customer, String itemName, double price, Container container) {
        double customerBalance = BalanceManager.getPlayerBalance(customer);
        if (customerBalance < price) {
            customer.sendSystemMessage(Component.translatable("shop.error.not_enough_money", String.format("%.2f", price))
                    .withStyle(ChatFormatting.RED));
            return;
        }

        ItemStack itemToTransfer = null;
        int containerSlot = -1;

        for (int i = 0; i < container.getContainerSize(); i++) {
            ItemStack stack = container.getItem(i);
            if (!stack.isEmpty() && matchesItemName(stack, itemName)) {
                itemToTransfer = stack.copy();
                containerSlot = i;
                break;
            }
        }

        if (itemToTransfer == null) {
            customer.sendSystemMessage(Component.translatable("shop.error.item_not_available")
                    .withStyle(ChatFormatting.RED));
            return;
        }

        ItemStack singleItem = itemToTransfer.copy();
        singleItem.setCount(1);

        if (!hasInventorySpace(customer.getInventory(), singleItem)) {
            customer.sendSystemMessage(Component.translatable("shop.error.inventory_full")
                    .withStyle(ChatFormatting.RED));
            return;
        }

        // Transaktion durchführen
        BalanceManager.updatePlayerBalance(customer, customerBalance - price);
        addBalance(ownerUUID.toString(), price);

        // Item transfer
        container.removeItem(containerSlot, 1);
        addToInventory(customer, singleItem.copy());

        sendNotifications(customer, ownerUUID, itemName, price, "buy");
    }

    private static void handleSellInteraction(UUID ownerUUID, ServerPlayer customer, String itemName, double price, Container container) {
        String ownerUUIDString = ownerUUID.toString();
        double ownerBalance = BalanceManager.getBalance(ownerUUIDString);

        if (ownerBalance < price) {
            customer.sendSystemMessage(Component.translatable("shop.error.owner_not_enough_money")
                    .withStyle(ChatFormatting.RED));
            return;
        }

        ItemStack itemToSell = null;
        int inventorySlot = -1;

        Inventory inventory = customer.getInventory();
        for (int i = 0; i < inventory.getContainerSize(); i++) {
            ItemStack stack = inventory.getItem(i);
            if (!stack.isEmpty() && matchesItemName(stack, itemName)) {
                itemToSell = stack.copy();
                inventorySlot = i;
                break;
            }
        }

        if (itemToSell == null) {
            customer.sendSystemMessage(Component.translatable("shop.error.item_not_in_inventory")
                    .withStyle(ChatFormatting.RED));
            return;
        }

        if (!hasContainerSpace(container, itemToSell)) {
            customer.sendSystemMessage(Component.translatable("shop.error.container_full")
                    .withStyle(ChatFormatting.RED));
            return;
        }

        // Transaktion durchführen
        addBalance(customer.getUUID().toString(), price);
        BalanceManager.setBalance(ownerUUIDString, ownerBalance - price);

        // Item transfer
        inventory.removeItem(inventorySlot, 1);
        addToContainer(container, itemToSell.copy());

        sendNotifications(customer, ownerUUID, itemName, price, "sell");
    }

    private static boolean matchesItemName(ItemStack stack, String searchName) {
        String stackName = stripFormatting(stack.getHoverName().getString());
        return stackName.equalsIgnoreCase(stripFormatting(searchName));
    }

    private static void addToInventory(ServerPlayer player, ItemStack itemStack) {
        Inventory inventory = player.getInventory();

        // First try to stack with existing items
        for (int i = 0; i < inventory.getContainerSize(); i++) {
            ItemStack stack = inventory.getItem(i);
            if (!stack.isEmpty() && stack.is(itemStack.getItem()) && stack.getCount() < stack.getMaxStackSize()) {
                stack.grow(1);
                inventory.setItem(i, stack);
                return;
            }
        }

        // Then try to find an empty slot
        for (int i = 0; i < inventory.getContainerSize(); i++) {
            if (inventory.getItem(i).isEmpty()) {
                inventory.setItem(i, itemStack);
                return;
            }
        }
    }

    private static void sendNotifications(ServerPlayer customer, UUID ownerUUID, String itemName, double price, String type) {
        if (type.equals("buy")) {
            // Nachricht an Käufer
            customer.sendSystemMessage(Component.translatable("shop.buy.success", itemName, String.format("%.2f", price))
                    .withStyle(Style.EMPTY.withColor(ChatFormatting.GREEN)));

            // Nachricht an Verkäufer
            ServerPlayer owner = customer.getServer().getPlayerList().getPlayer(ownerUUID);
            if (owner != null) {
                owner.sendSystemMessage(Component.translatable("shop.buy.owner_notification",
                        customer.getDisplayName().getString(), itemName, String.format("%.2f", price))
                        .withStyle(Style.EMPTY.withColor(ChatFormatting.GREEN)));
            }
        } else {
            // Nachricht an Verkäufer
            customer.sendSystemMessage(Component.translatable("shop.sell.success", itemName, String.format("%.2f", price))
                    .withStyle(Style.EMPTY.withColor(ChatFormatting.GREEN)));

            // Nachricht an Käufer
            ServerPlayer owner = customer.getServer().getPlayerList().getPlayer(ownerUUID);
            if (owner != null) {
                owner.sendSystemMessage(Component.translatable("shop.sell.owner_notification",
                        customer.getDisplayName().getString(), itemName, String.format("%.2f", price))
                        .withStyle(Style.EMPTY.withColor(ChatFormatting.GREEN)));
            }
        }
    }

    private static void addBalance(String playerUUID, double amount) {
        double current = BalanceManager.getBalance(playerUUID);
        BalanceManager.setBalance(playerUUID, current + amount);
    }

    private static boolean hasInventorySpace(Inventory inventory, ItemStack itemStack) {
        for (int i = 0; i < inventory.getContainerSize(); i++) {
            ItemStack stack = inventory.getItem(i);
            if (stack.isEmpty()) return true;
            if (stack.is(itemStack.getItem()) && stack.getCount() < stack.getMaxStackSize()) return true;
        }
        return false;
    }

    private static boolean hasContainerSpace(Container container, ItemStack itemStack) {
        for (int i = 0; i < container.getContainerSize(); i++) {
            ItemStack stack = container.getItem(i);
            if (stack.isEmpty()) return true;
            if (stack.is(itemStack.getItem()) && stack.getCount() < stack.getMaxStackSize()) return true;
        }
        return false;
    }

    private static void addToContainer(Container container, ItemStack itemStack) {
        for (int i = 0; i < container.getContainerSize(); i++) {
            ItemStack stack = container.getItem(i);
            if (!stack.isEmpty() && stack.is(itemStack.getItem()) && stack.getCount() < stack.getMaxStackSize()) {
                stack.grow(1);
                container.setItem(i, stack);
                return;
            }
        }
        for (int i = 0; i < container.getContainerSize(); i++) {
            if (container.getItem(i).isEmpty()) {
                container.setItem(i, itemStack);
                return;
            }
        }
    }
}
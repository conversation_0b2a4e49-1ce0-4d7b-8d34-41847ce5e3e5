{"item.saros__money_mod.k_2_paycheck": "Kreditkarte", "item.saros__money_mod.k_5_paycheck": "Kreditkarte", "command.pay.not_enough_money": "Du hast nicht genug Geld!", "command.eco.set.success": "Kontostand von %2$s auf %1$d gesetzt.", "lang.deposit.new_balance": "Neuer Kontostand: ", "item.saros_projecte_money_mod.k_3_paycheck": "Kreditkarte", "item.saros__money_mod.euro_20": "20 Dollar", "gui.saros__money_mod.balance": "Kontostand: %d", "block.saros__money_mod.atm_2": "ATM", "lang.withdraw.not_enough_inventory": "Nicht genügend Platz im Inventar!", "item.saros_projecte_money_mod.euro_20": "20 Euro", "block.saros__money_mod.atm_3": "ATM", "message.death_money_loss": "Du hast %s <PERSON>eld beim Tod verloren. Dein neuer Kontostand beträgt %s.", "itemGroup.tabsaros_project_e_money_mod": "<PERSON><PERSON>", "message.sarosmoneymod.paycheck": "Du hast dein Gehalt von %s erhalten!", "item.saros_projecte_money_mod.k_10_paycheck": "Kreditkarte", "gui.saros__money_mod.atmgui.withdraw": "Au<PERSON>ahl<PERSON>", "item.saros__money_mod.euro_2": "2 Dollar", "gui.saros__money_mod.atmgui.output": "Au<PERSON>ahl<PERSON>", "item.saros_projecte_money_mod.euro_200": "200 Euro", "item.saros__money_mod.euro_5": "5 Euro", "item.saros__money_mod.euro_50": "50 Euro", "lang.withdraw.enter_valid_number": "Bitte eine gültige Zahl e<PERSON>ben!", "block.saros__money_mod.atm": "ATM", "item.saros__money_mod.euro_10": "10 Euro", "command.leaderboard.create.failure": "Ein Fehler ist aufgetreten, als versucht wurde, den Spieler zu bekommen.", "item.saros_projecte_money_mod.euro_50": "50 Euro", "item.saros_projecte_money_mod.euro_10": "10 Euro", "item.saros__money_mod.euro_1": "1 Euro", "item.saros_projecte_money_mod.euro_5": "5 Euro", "lang.withdraw.withdrawn_amount": "Abgehobener Betrag: ", "lang.withdraw.not_enough_balance": "Nicht genügend Guthaben auf dem Konto!", "item.saros__money_mod.euro_500": "500 Euro", "item.saros_projecte_money_mod.euro_2": "2 Euro", "command.eco.player_not_found": "Spieler nicht gefunden.", "item.saros_projecte_money_mod.euro_1": "1 Euro", "lang.withdraw.previous_balance": "<PERSON><PERSON><PERSON><PERSON>: ", "lang.withdraw.account_not_found": "Konto nicht gefunden!", "command.pay.success_receiver": "Du hast %s von %s erhalten.", "item.saros__money_mod.k_4_paycheck": "Kreditkarte", "item.saros_projecte_money_mod.k_4_paycheck": "Kreditkarte", "item.saros__money_mod.euro_100": "100 Euro", "gui.saros__money_mod.atmgui.button_payout": "<PERSON><PERSON><PERSON><PERSON>", "lang.deposit.added_amount": "Hinzugefügter Betrag: ", "command.eco.take.success": "Genommen %1$d von %2$s's Konto. Neuer Kontostand: %3$d.", "gui.saros__money_mod.atmgui.balance_label": "Kontostand: €", "lang.deposit.previous_balance": "<PERSON><PERSON><PERSON><PERSON>: ", "command.money.not_a_player": "<PERSON><PERSON> Befehl kann nur von einem Spieler ausgeführt werden.", "gui.saros__money_mod.atmgui.label_kontostand_value": "Kontostand: <value>", "item.saros_projecte_money_mod.k_5_paycheck": "Kreditkarte", "command.eco.get.success": "%1$s's Kontostand: %2$d.", "command.leaderboard.create.success": "Bestenliste erstellt!", "item.saros__money_mod.k_3_paycheck": "Kreditkarte", "itemGroup.tabsaros_money_mod": "Saros Money Mod", "gui.saros__money_mod.atmgui.button_deposit": "Ein<PERSON><PERSON><PERSON>", "command.pay.player_not_found": "Zielspieler nicht gefunden!", "item.saros__money_mod.k_10_paycheck": "Credit Card", "lang.withdraw.enter_positive_number": "Bitte eine positive Z<PERSON> e<PERSON>ben!", "item.saros_projecte_money_mod.euro_500": "500 Euro", "item.saros__money_mod.wallet": "<PERSON><PERSON>", "item.saros__money_mod.wallet_black": "Schwa<PERSON><PERSON> G<PERSON>l", "item.saros__money_mod.wallet_blue": "<PERSON><PERSON><PERSON>", "item.saros__money_mod.wallet_brown": "<PERSON><PERSON> Geldbeutel", "item.saros__money_mod.wallet_green": "<PERSON><PERSON><PERSON><PERSON>", "item.saros__money_mod.wallet_orange": "<PERSON><PERSON>", "item.saros__money_mod.wallet_purple": "<PERSON>", "item.saros__money_mod.wallet_yellow": "<PERSON><PERSON><PERSON>", "item.saros_projecte_money_mod.euro_100": "100 Euro", "command.eco.add.success": "Hinzugefügt %1$d zu %2$s's Konto. Neuer Kontostand: %3$d.", "lang.withdraw.new_balance": "Neuer Kontostand: ", "item.saros__money_mod.k_1_paycheck": "Kreditkarte", "item.saros_projecte_money_mod.k_2_paycheck": "Kreditkarte", "command.leaderboard.delete.success": "Alle Bestenlisten innerhalb von 5 Blöcken wurden gelöscht!", "command.money.balance": "Dein aktuelles Guthaben beträgt: %s", "command.eco.error": "Ein Fehler ist aufgetreten.", "item.saros__money_mod.euro_200": "200 Euro", "command.leaderboard.delete.failure": "Ein Fehler ist aufgetreten, als versucht wurde, den Spieler zu bekommen.", "item_group.saros__money_mod.saros_money_mod": "Saros Money Mod", "item.saros_projecte_money_mod.k_1_paycheck": "Kreditkarte", "command.leaderboard.unknown_player": "Unbekannter Spieler", "command.pay.success_sender": "Du hast %s an %s überwiesen.", "entity.minecraft.villager.saros__money_mod.banker": "Banker", "item.saros__money_mod.cent_1": "1 Cent", "item.saros__money_mod.cent_2": "2 Cent", "item.saros__money_mod.cent_5": "5 Cent", "item.saros__money_mod.cent_10": "10 Cent", "item.saros__money_mod.cent_20": "20 Cent", "item.saros__money_mod.cent_50": "50 Cent", "block.saros__money_mod.banker": "Bankertisch", "shop.error.no_container": "Kein Container gefunden!", "shop.error.not_enough_money": "Du hast nicht genug Geld! Benötigt: $%.2f", "shop.error.item_not_available": "Das <PERSON>em ist nicht mehr verfügbar!", "shop.error.inventory_full": "Dein Inventar ist voll!", "shop.error.owner_not_enough_money": "Der Shop-Besitzer hat nicht genug Geld!", "shop.error.item_not_in_inventory": "Du hast das Item nicht in deinem Inventar!", "shop.error.container_full": "Der Container ist voll!", "shop.error.invalid_shop_type": "Ungültiger Shop-Typ! Benutze 'buy' oder 'sell'", "shop.error.invalid_price_format": "Ungültiges Preisformat!", "shop.protection.container_owner_only": "<PERSON>ur der Besitzer kann dies entfernen!", "shop.protection.sign_owner_only": "Nur der Besitzer kann dieses Schild entfernen!", "shop.protection.container_use_owner_only": "<PERSON><PERSON> der Besitzer kann dies benutzen!", "shop.buy.success": "Kauf-Shop: Du hast %s für $%.2f gekauft", "shop.buy.owner_notification": "%s hat in deinem Kauf-Shop %s für $%.2f gekauft", "shop.sell.success": "Verkauf-Shop: Du hast %s für $%.2f verkauft", "shop.sell.owner_notification": "%s hat in deinem Verkauf-Shop %s für $%.2f verkauft"}
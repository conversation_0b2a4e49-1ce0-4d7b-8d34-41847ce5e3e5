package de.sarocesch.sarosmoneymod.network;

import de.sarocesch.sarosmoneymod.handlers.ServerHandler;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.server.TickTask;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.SignBlockEntity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class SignUpdatePacket {
    private static final Logger LOGGER = LogManager.getLogger();
    private final BlockPos pos;

    public SignUpdatePacket(BlockPos pos) {
        this.pos = pos;
    }

    public SignUpdatePacket(FriendlyByteBuf buf) {
        this.pos = buf.readBlockPos();
    }

    public void encode(FriendlyByteBuf buf) {
        buf.writeBlockPos(pos);
    }

    // Process the packet on the server side
    public void process(ServerPlayer player) {
        if (player == null) {
            return;
        }

        Level level = player.level();
        
        // Force chunk loading
        if (level instanceof ServerLevel serverLevel) {
            serverLevel.getChunk(pos.getX() >> 4, pos.getZ() >> 4);
        }
        
        if (level.getBlockEntity(pos) instanceof SignBlockEntity sign) {
            // Process sign first
            ServerHandler.handleSignUpdate(player, pos);
            
            // Then update persistence and sync
            sign.setChanged();
            level.getChunkAt(pos).setUnsaved(true);
            
            // Update adjacent blocks
            for (Direction dir : Direction.values()) {
                level.updateNeighborsAt(pos.relative(dir), level.getBlockState(pos).getBlock());
            }

            // Add multiple delayed updates to ensure synchronization
            if (level.getServer() != null) {
                // First update after 1 tick
                level.getServer().tell(new TickTask(
                    level.getServer().getTickCount() + 1,
                    () -> ServerHandler.handleSignUpdate(player, pos)
                ));
                
                // Second update after 3 ticks
                level.getServer().tell(new TickTask(
                    level.getServer().getTickCount() + 3,
                    () -> {
                        level.sendBlockUpdated(pos, 
                            level.getBlockState(pos), 
                            level.getBlockState(pos), 
                            3 // Flags: 1 + 2
                        );
                        ServerHandler.handleSignUpdate(player, pos);
                    }
                ));
            }
        }
    }
}
package de.sarocesch.sarosmoneymod.item;

import net.minecraft.world.level.Level;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.InteractionHand;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.core.component.DataComponents;
import net.minecraft.world.item.component.CustomData;

import de.sarocesch.sarosmoneymod.world.inventory.WalletGUIMenu;
// import de.sarocesch.sarosmoneymod.SarosMoneyModMod; // Not used directly
// import javax.annotation.Nullable; // Not used directly

import io.netty.buffer.Unpooled;

public class GreenWalletItem extends Item {
	public GreenWalletItem() {
		super(new Item.Properties().stacksTo(1).rarity(Rarity.COMMON));
	}

	@Override
	public InteractionResultHolder<ItemStack> use(Level world, Player entity, InteractionHand hand) {
		InteractionResultHolder<ItemStack> ar = super.use(world, entity, hand);
		ItemStack itemstack = ar.getObject();
		// double x = entity.getX(); // Not used
		// double y = entity.getY(); // Not used
		// double z = entity.getZ(); // Not used

		if (!world.isClientSide()) {
			// Stelle sicher, dass die Inventardaten initialisiert sind
			initInventoryData(itemstack);

			entity.openMenu(new MenuProvider() {
				@Override
				public Component getDisplayName() {
					return Component.literal("Green Wallet");
				}

				@Override
				public AbstractContainerMenu createMenu(int id, Inventory inventory, Player player) {
					FriendlyByteBuf packetBuffer = new FriendlyByteBuf(Unpooled.buffer());
					packetBuffer.writeBlockPos(entity.blockPosition());
					packetBuffer.writeInt(hand == InteractionHand.MAIN_HAND ? 0 : 1); // Handindex updated
					return new WalletGUIMenu(id, inventory, packetBuffer);
				}
			});
		}

		return ar;
	}

	// Helper method for Data Components API (from BlackWalletItem.java)
	private void initInventoryData(ItemStack stack) {
		if (!stack.has(DataComponents.CUSTOM_DATA)) {
			CompoundTag nbt = new CompoundTag();
			// Initialisiere das Inventar mit 27 Slots
			CompoundTag inventoryTag = new CompoundTag();
			inventoryTag.putInt("Size", 27); // Updated to 27 slots for 3 rows
			nbt.put("Inventory", inventoryTag);
			stack.set(DataComponents.CUSTOM_DATA, CustomData.of(nbt));
		}
	}
}

package de.sarocesch.sarosmoneymod.handlers;

// Add this import at the top with others
import net.minecraft.world.level.ChunkPos;
import java.util.Arrays;
import java.util.List;

import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.network.protocol.game.ClientboundBlockEntityDataPacket;
import net.minecraft.network.protocol.game.ClientboundBlockUpdatePacket;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Block;
import javax.annotation.Nullable;
import net.minecraft.world.Container;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.SignBlockEntity;
import net.minecraft.world.level.block.entity.SignText;
import net.minecraft.world.level.block.entity.SignText;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.RotationSegment;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ServerHandler {
    private static final Logger LOGGER = LogManager.getLogger();

    public static void handleSignUpdate(@Nullable ServerPlayer player, BlockPos signPos) {
        Level level = player != null ? player.level() : null;

        if (level == null) {
            LOGGER.warn("[Shop] Cannot process sign update: invalid level");
            return;
        }

        // Force chunk loading with full status
        if (level instanceof ServerLevel serverLevel) {
            serverLevel.getChunk(signPos.getX() >> 4, signPos.getZ() >> 4);
        }

        if (level.getBlockEntity(signPos) instanceof SignBlockEntity sign) {
            // Immediately update sign text
            sign.setChanged();

            BlockState signState = level.getBlockState(signPos);
            BlockPos containerPos = ContainerHandler.getAttachedContainerPos(level, signPos, signState);

            // Force container chunk loading
            if (level instanceof ServerLevel serverLevel) {
                serverLevel.getChunk(containerPos.getX() >> 4, containerPos.getZ() >> 4);
                // Force load the container entity
                BlockEntity containerEntity = level.getBlockEntity(containerPos);
                if (containerEntity != null) {
                    containerEntity.setChanged(); // Mark container as changed to ensure it's saved
                }
            }

            // Get item name
            MutableComponent itemName = getItemName(level, containerPos);
            
            // Update sign text with the item name
            boolean updated = updateSignText(sign, containerPos, itemName);
            
            if (updated) {
                // Force immediate sync to client
                syncSignWithClient(level, signPos, sign);
                
                // Also update the sign in the SignShopListener for interaction
                if (level.getServer() != null) {
                    level.getServer().tell(new net.minecraft.server.TickTask(
                        level.getServer().getTickCount() + 1,
                        () -> {
                            // Force another sync after a tick
                            syncSignWithClient(level, signPos, sign);
                            
                            // Force block update to all clients
                            level.sendBlockUpdated(
                                signPos,
                                signState,
                                signState,
                                Block.UPDATE_ALL
                            );
                        }
                    ));
                }
            }
        }
    }

    private static boolean updateSignText(SignBlockEntity sign, BlockPos containerPos, MutableComponent itemName) {
        SignText frontText = sign.getText(true);
        Component[] lines = frontText.getMessages(true);

        String firstLine = stripFormatting(lines[0]).trim().toLowerCase();
        boolean isShop = firstLine.equals("[shop]");
        boolean isBuy = firstLine.equals("[buy]");
        boolean isSell = firstLine.equals("[sell]");

        if (!isShop && !isBuy && !isSell) {
            return false;
        }

        ChatFormatting color = isBuy ? ChatFormatting.BLUE
                : isSell ? ChatFormatting.GOLD
                : ChatFormatting.DARK_GREEN;

        Component[] newLines = new Component[4];

        // Zeile 0: Shop-Typ
        newLines[0] = Component.literal(isShop ? "[SHOP]" : isBuy ? "[BUY]" : "[SELL]")
                .withStyle(Style.EMPTY.withBold(true).withColor(color));

        // Zeile 1: Keep original or set empty
        newLines[1] = lines[1].copy();

        // Zeile 2: Item-Name
        newLines[2] = itemName;

        // Zeile 3: Preis
        try {
            String priceText = stripFormatting(lines[3])
                    .replaceAll("[^\\d.,]", "")
                    .replace(',', '.');

            if (priceText.isEmpty()) {
                throw new NumberFormatException("Empty price");
            }

            double price = Double.parseDouble(priceText);
            newLines[3] = Component.literal("$" + String.format("%.2f", price))
                    .withStyle(Style.EMPTY.withBold(true).withColor(color));
        } catch (Exception e) {
            LOGGER.error("Invalid price format: {}", e.getMessage());
            newLines[3] = Component.literal("Invalid Price")
                    .withStyle(Style.EMPTY.withColor(ChatFormatting.RED));
        }



        // Add item validation
        if (itemName.getString().isEmpty()) {
            LOGGER.error("[Shop Error] Missing item in container at {}", containerPos);
            newLines[2] = Component.literal("!NO ITEM!").withStyle(ChatFormatting.RED);
        }

        // Create new SignText with our updated lines
        SignText newSignText = new SignText(
                newLines,
                newLines, // Use same for filtered
                frontText.getColor(),
                frontText.hasGlowingText()
        );
        
        // Set both front and back text to ensure consistency
        sign.setText(newSignText, true);
        sign.setText(newSignText, false);
        
        // Critical: Mark sign as changed and force chunk to save
        sign.setChanged();
        if (sign.getLevel() != null) {
            sign.getLevel().getChunkAt(sign.getBlockPos()).setUnsaved(true);
        }
        
        return true;
    }

    private static void syncSignWithClient(Level level, BlockPos pos, SignBlockEntity sign) {
        if (!(level instanceof ServerLevel serverLevel)) {
            return;
        }
        
        // Create and send block entity data packet
        ClientboundBlockEntityDataPacket packet = ClientboundBlockEntityDataPacket.create(sign);
        
        // Send to all players tracking this chunk
        serverLevel.getChunkSource().chunkMap.getPlayers(
            serverLevel.getChunkAt(pos).getPos(), false
        ).forEach(player -> player.connection.send(packet));
        
        // Force block update to all clients
        BlockState state = level.getBlockState(pos);
        level.sendBlockUpdated(pos, state, state, Block.UPDATE_ALL);
    }

    private static MutableComponent getItemName(Level level, BlockPos pos) {
        BlockEntity be = level.getBlockEntity(pos);
        if (be instanceof Container container) {
            // Search all slots for first non-empty stack
            for (int i = 0; i < container.getContainerSize(); i++) {
                ItemStack stack = container.getItem(i);
                if (!stack.isEmpty()) {
                    // Use original item name with formatting removed
                    String rawName = stripFormatting(stack.getHoverName());
                    return Component.literal(rawName)
                            .withStyle(Style.EMPTY.withBold(true));
                }
            }
        }
        return Component.literal("").withStyle(Style.EMPTY); // Return empty instead of "Air"
    }

    private static String stripFormatting(Component component) {
        return component.getString().replaceAll("§[0-9a-fk-or]", "");
    }

    private static Item getItemFromContainer(BlockPos containerPos, Level level) {
        BlockEntity be = level.getBlockEntity(containerPos);

        if (be instanceof Container container) {
            for (int i = 0; i < container.getContainerSize(); i++) {
                ItemStack stack = container.getItem(i);
                if (!stack.isEmpty()) {
                    return stack.getItem();
                }
            }
        }
        return Items.AIR;
    }
}

package de.sarocesch.sarosmoneymod.data;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.level.storage.LevelResource;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * Diese Klasse migriert alte Bank-Dateien (im alten Geldsystem)
 * in das neue SavedData-basierte System.
 *
 * Es werden alle .bank-Dateien im Ordner "bank-account" des Weltordners gelesen.
 * Für jede Datei wird der in ihr gespeicherte Kontostand (Integer) ausgelesen und
 * über den BalanceManager in das neue System übernommen – vorhandene Werte werden überschrieben.
 * Anschließend wird die Datei gelöscht; ist der Ordner leer, wird auch dieser gelöscht.
 */
public class BalanceMigrator {

    private static boolean migrated = false; // Sorgt dafür, dass die Migration nur einmal erfolgt

    public static void migrateOldData(MinecraftServer server) {
        if (migrated) {
            return;
        }
        // Hole den Overworld-Level
        ServerLevel level = server.overworld();
        Path worldDir = server.getWorldPath(LevelResource.ROOT);
        File oldBankDir = new File(worldDir.toFile(), "bank-account");

        if (!oldBankDir.exists() || !oldBankDir.isDirectory()) {
            migrated = true;
            return;
        }

        File[] bankFiles = oldBankDir.listFiles((dir, name) -> name.endsWith(".bank"));
        if (bankFiles != null) {
            for (File file : bankFiles) {
                String fileName = file.getName(); // z.B. "playerUUID.bank"
                // Extrahiere die Spieler-UUID aus dem Dateinamen
                String playerUUID = fileName.substring(0, fileName.lastIndexOf(".bank"));
                try {
                    String content = new String(Files.readAllBytes(file.toPath())).trim();
                    double balance = Double.parseDouble(content);
                    // Falls es ein Integer-Wert ist, konvertiere ihn zu Double
                    if (content.indexOf('.') == -1) {
                        balance = (double) Integer.parseInt(content);
                    }
                    // Übertrage den Wert in das neue System – vorhandene Einträge werden überschrieben!
                    BalanceManager.setBalance(playerUUID, balance);
                    // Lösche die alte Datei
                    Files.delete(file.toPath());
                } catch (IOException | NumberFormatException e) {
                    e.printStackTrace();
                }
            }
        }

        // Lösche den Ordner, falls er leer ist
        File[] remainingFiles = oldBankDir.listFiles();
        if (remainingFiles == null || remainingFiles.length == 0) {
            try {
                Files.delete(oldBankDir.toPath());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        migrated = true;
        System.out.println("[SarosMoneyMod] Old bank data has been migrated.");
    }
}

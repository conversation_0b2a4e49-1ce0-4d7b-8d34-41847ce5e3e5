package de.sarocesch.sarosmoneymod.item;

// import net.minecraft.core.HolderLookup; // No longer needed
// import net.minecraftforge.items.ItemStackHandler; // No longer needed
// import net.minecraftforge.common.capabilities.ICapabilityProvider; // No longer needed

import net.minecraft.world.level.Level;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.InteractionHand;
// import net.minecraft.server.level.ServerPlayer; // Not directly used
import net.minecraft.network.chat.Component;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.core.component.DataComponents;
import net.minecraft.world.item.component.CustomData;

import de.sarocesch.sarosmoneymod.world.inventory.WalletGUIMenu;
// import de.sarocesch.sarosmoneymod.SarosMoneyModMod; // Not used directly
// import javax.annotation.Nullable; // Not used directly

import io.netty.buffer.Unpooled;

public class OrangeWalletItem extends Item {
	public OrangeWalletItem() {
		super(new Item.Properties().stacksTo(1).rarity(Rarity.COMMON));
	}

	@Override
	public InteractionResultHolder<ItemStack> use(Level world, Player entity, InteractionHand hand) {
		InteractionResultHolder<ItemStack> ar = super.use(world, entity, hand);
		ItemStack itemstack = ar.getObject();
		// double x = entity.getX(); // Not used
		// double y = entity.getY(); // Not used
		// double z = entity.getZ(); // Not used

		if (!world.isClientSide()) {
			// Stelle sicher, dass die Inventardaten initialisiert sind
			initInventoryData(itemstack);

			entity.openMenu(new MenuProvider() {
				@Override
				public Component getDisplayName() {
					return Component.literal("Orange Wallet");
				}

				@Override
				public AbstractContainerMenu createMenu(int id, Inventory inventory, Player player) {
					FriendlyByteBuf packetBuffer = new FriendlyByteBuf(Unpooled.buffer());
					packetBuffer.writeBlockPos(entity.blockPosition());
					packetBuffer.writeInt(hand == InteractionHand.MAIN_HAND ? 0 : 1); // Handindex updated
					return new WalletGUIMenu(id, inventory, packetBuffer);
				}
			});
		}

		return ar;
	}

	// Helper method for Data Components API (from BlackWalletItem.java)
	private void initInventoryData(ItemStack stack) {
		if (!stack.has(DataComponents.CUSTOM_DATA)) {
			CompoundTag nbt = new CompoundTag();
			// Initialisiere das Inventar mit 18 Slots
			CompoundTag inventoryTag = new CompoundTag();
			inventoryTag.putInt("Size", 27); // Added Size initialization
			nbt.put("Inventory", inventoryTag);
			stack.set(DataComponents.CUSTOM_DATA, CustomData.of(nbt));
		}
	}
}

package de.sarocesch.sarosmoneymod.network;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.codec.StreamCodec;
import net.minecraft.network.protocol.common.custom.CustomPacketPayload;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.neoforged.neoforge.network.PacketDistributor;
import net.neoforged.neoforge.network.event.RegisterPayloadHandlersEvent;
import net.neoforged.neoforge.network.handling.IPayloadContext;
import net.neoforged.neoforge.network.registration.PayloadRegistrar;

public class NetworkHandler {
    public static void register(final RegisterPayloadHandlersEvent event) {
        PayloadRegistrar registrar = event.registrar(SarosMoneyModMod.MODID)
                .versioned("1");

        // Registrierung mit dem neuen Payload-Typ
        registrar.playToServer(
                ATMGUIButtonMessage.TYPE,
                ATMGUIButtonMessage.STREAM_CODEC,
                NetworkHandler::handleMessage
        );
    }

    private static void handleMessage(ATMGUIButtonMessage msg, IPayloadContext context) {
        context.enqueueWork(() -> {
            if (context.player() instanceof ServerPlayer sender) {
                ATMGUIButtonMessage.handle(msg, sender);
            } else {
                SarosMoneyModMod.LOGGER.error("Received ATMGUIButtonMessage but sender is not a ServerPlayer!");
            }
        });
    }

    public static void sendToServer(CustomPacketPayload message) {
        PacketDistributor.sendToServer(message);
    }
}
package de.sarocesch.sarosmoneymod.network;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import de.sarocesch.sarosmoneymod.procedures.DepositProcedure;
import de.sarocesch.sarosmoneymod.procedures.WithdrawProcedure;
import de.sarocesch.sarosmoneymod.world.inventory.ATMGUIMenu;
import net.minecraft.core.BlockPos;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.codec.StreamCodec;
import net.minecraft.network.protocol.common.custom.CustomPacketPayload;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

import java.util.HashMap;

public record ATMGUIButtonMessage(int buttonID, BlockPos pos, String withdrawValue) implements CustomPacketPayload {
    public static final ResourceLocation ID = ResourceLocation.fromNamespaceAndPath(SarosMoneyModMod.MODID, "atmgui_button");
    public static final Type<ATMGUIButtonMessage> TYPE = new Type<>(ID);

    public static final StreamCodec<FriendlyByteBuf, ATMGUIButtonMessage> STREAM_CODEC = StreamCodec.of(
            (buf, msg) -> {
                buf.writeInt(msg.buttonID());
                buf.writeBlockPos(msg.pos());
                buf.writeUtf(msg.withdrawValue(), 32767);
            },
            buf -> new ATMGUIButtonMessage(buf.readInt(), buf.readBlockPos(), buf.readUtf(32767))
    );

    @Override
    public Type<? extends CustomPacketPayload> type() {
        return TYPE;
    }

    public static void handle(ATMGUIButtonMessage message, Player player) {
        if (player instanceof ServerPlayer serverPlayer) {
            Level world = serverPlayer.level();
            HashMap<String, Object> guistate = ATMGUIMenu.guistate;

            try {
                String value = message.withdrawValue();
                if (value == null || value.trim().isEmpty()) {
                    value = "0";
                }

                switch (message.buttonID()) {
                    case 0 -> WithdrawProcedure.execute(serverPlayer, guistate, value, serverPlayer);
                    case 1 -> DepositProcedure.execute(serverPlayer, guistate, value, serverPlayer);
                    default -> SarosMoneyModMod.LOGGER.warn("Unknown button ID: " + message.buttonID());
                }

                double playerBalance = BalanceManager.getPlayerBalance(serverPlayer);
                int newBalance = (int) (playerBalance * 100);

                if (serverPlayer.containerMenu instanceof ATMGUIMenu menu) {
                    menu.balance.set(newBalance);
                    menu.broadcastChanges();
                }
            } catch (Exception e) {
                SarosMoneyModMod.LOGGER.error("Error handling ATM button click", e);
            }
        } else {
            SarosMoneyModMod.LOGGER.error("Player is not a ServerPlayer!");
        }
    }
}
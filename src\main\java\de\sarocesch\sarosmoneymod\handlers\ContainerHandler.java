package de.sarocesch.sarosmoneymod.handlers;

import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.world.Container;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ContainerHandler {
    private static final Logger LOGGER = LogManager.getLogger();

    public static BlockPos getAttachedContainerPos(Level level, BlockPos signPos, BlockState signState) {
        Direction facing = Direction.NORTH;

        if (signState.hasProperty(BlockStateProperties.HORIZONTAL_FACING)) {
            facing = signState.getValue(BlockStateProperties.HORIZONTAL_FACING);
        } else if (signState.hasProperty(BlockStateProperties.ROTATION_16)) {
            int rotation = signState.getValue(BlockStateProperties.ROTATION_16);
            // Fix the direction calculation for standing signs
            facing = Direction.from2DDataValue(rotation / 2); // Corrected calculation
        }

        BlockPos containerPos = signPos.relative(facing.getOpposite());
        
        // Verify if there's actually a container at the calculated position
        BlockEntity be = level.getBlockEntity(containerPos);
        if (!(be instanceof Container)) {
            // Try alternative positions if no container found at the expected position
            for (Direction dir : Direction.values()) {
                if (dir == facing) continue; // Skip the direction we already checked
                
                BlockPos altPos = signPos.relative(dir);
                BlockEntity altBe = level.getBlockEntity(altPos);
                
                if (altBe instanceof Container) {
                    return altPos;
                }
            }
        }
        
        return containerPos;
    }

    public static MutableComponent getFirstItemName(Level level, BlockPos containerPos) {
        BlockEntity be = level.getBlockEntity(containerPos);
        MutableComponent line = Component.literal("Air").withStyle(Style.EMPTY.withBold(true));

        if (be instanceof Container container) {
            for (int i = 0; i < container.getContainerSize(); i++) {
                ItemStack stack = container.getItem(i);
                if (!stack.isEmpty()) {
                    return stack.getHoverName().copy().withStyle(Style.EMPTY.withBold(true));
                }
            }
        } else {
            LOGGER.warn("Kein Container an {}", containerPos);
        }
        return line;
    }
}
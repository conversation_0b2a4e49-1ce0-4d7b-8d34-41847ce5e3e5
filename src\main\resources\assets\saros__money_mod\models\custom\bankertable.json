{"credit": "Made with Blockbench", "texture_size": [128, 128], "textures": {"0": "bankertable:200euro", "1": "bankertable:500euro", "2": "bankertable:10euro", "3": "bankertable:feather", "5": "bankertable:tint", "6": "bankertable:tableplate", "7": "bankertable:gold", "8": "bankertable:tablebase", "9": "bankertable:door", "10": "bankertable:tabel<PERSON>", "particle": "bankertable:200euro"}, "elements": [{"from": [-8, 11, 0], "to": [24, 13, 16], "rotation": {"angle": 0, "axis": "y", "origin": [-8, 11, 0]}, "faces": {"north": {"uv": [0, 8, 8, 8.5], "texture": "#6"}, "east": {"uv": [8, 0.5, 12, 1], "texture": "#6"}, "south": {"uv": [8, 0, 16, 0.5], "texture": "#6"}, "west": {"uv": [8, 1, 12, 1.5], "texture": "#6"}, "up": {"uv": [8, 4, 0, 0], "texture": "#6"}, "down": {"uv": [8, 4, 0, 8], "texture": "#6"}}}, {"from": [-8, 0, 1], "to": [-6.5, 11, 16], "rotation": {"angle": 0, "axis": "y", "origin": [-7.5, 0, 0.5]}, "faces": {"north": {"uv": [2.375, 5.625, 2.625, 7], "texture": "#8"}, "east": {"uv": [3.75, 2.75, 5.625, 4.125], "texture": "#8"}, "south": {"uv": [2.625, 5.625, 2.875, 7], "texture": "#8"}, "west": {"uv": [0, 3.875, 1.875, 5.25], "texture": "#8"}, "up": {"uv": [1.875, 7.125, 1.625, 5.25], "texture": "#8"}, "down": {"uv": [5.875, 0, 5.625, 1.875], "texture": "#8"}}}, {"from": [22.5, 0, 1], "to": [24, 11, 16], "rotation": {"angle": 0, "axis": "y", "origin": [23, 0, 0.5]}, "faces": {"north": {"uv": [5.375, 0, 5.625, 1.375], "texture": "#8"}, "east": {"uv": [0, 2.5, 1.875, 3.875], "texture": "#8"}, "south": {"uv": [1.875, 5.625, 2.125, 7], "texture": "#8"}, "west": {"uv": [1.875, 2.5, 3.75, 3.875], "texture": "#8"}, "up": {"uv": [0.875, 7.125, 0.625, 5.25], "texture": "#8"}, "down": {"uv": [1.125, 5.25, 0.875, 7.125], "texture": "#8"}}}, {"from": [16, 0, 1], "to": [17.5, 11, 16], "rotation": {"angle": 0, "axis": "y", "origin": [16.5, 0, 0.5]}, "faces": {"north": {"uv": [2.125, 5.625, 2.375, 7], "texture": "#8"}, "east": {"uv": [2.875, 0, 4.75, 1.375], "texture": "#8"}, "south": {"uv": [5.625, 2.125, 5.875, 3.5], "texture": "#8"}, "west": {"uv": [3.75, 1.375, 5.625, 2.75], "texture": "#8"}, "up": {"uv": [1.375, 7.125, 1.125, 5.25], "texture": "#8"}, "down": {"uv": [1.625, 5.25, 1.375, 7.125], "texture": "#8"}}}, {"from": [17.5, 10, 1], "to": [22.5, 11, 15], "rotation": {"angle": 0, "axis": "y", "origin": [18, 10, 0.5]}, "faces": {"north": {"uv": [2.875, 1.625, 3.5, 1.75], "texture": "#8"}, "east": {"uv": [5.625, 3.75, 7.375, 3.875], "texture": "#8"}, "south": {"uv": [2.875, 1.75, 3.5, 1.875], "texture": "#8"}, "west": {"uv": [5.625, 3.875, 7.375, 4], "texture": "#8"}, "up": {"uv": [3.75, 5.625, 3.125, 3.875], "texture": "#8"}, "down": {"uv": [4.375, 4.125, 3.75, 5.875], "texture": "#8"}}}, {"from": [17.5, 7, 1], "to": [22.5, 8, 15], "rotation": {"angle": 0, "axis": "y", "origin": [18, 7, 0.5]}, "faces": {"north": {"uv": [2.875, 1.375, 3.5, 1.5], "texture": "#8"}, "east": {"uv": [5.625, 3.5, 7.375, 3.625], "texture": "#8"}, "south": {"uv": [2.875, 1.5, 3.5, 1.625], "texture": "#8"}, "west": {"uv": [5.625, 3.625, 7.375, 3.75], "texture": "#8"}, "up": {"uv": [2.5, 5.625, 1.875, 3.875], "texture": "#8"}, "down": {"uv": [3.125, 3.875, 2.5, 5.625], "texture": "#8"}}}, {"from": [17.5, 1, 1], "to": [22.5, 1.5, 15], "rotation": {"angle": 0, "axis": "y", "origin": [18, 0.5, 0.5]}, "faces": {"north": {"uv": [2.875, 1.875, 3.5, 2], "texture": "#8"}, "east": {"uv": [5.625, 4, 7.375, 4.125], "texture": "#8"}, "south": {"uv": [2.875, 2, 3.5, 2.125], "texture": "#8"}, "west": {"uv": [5.625, 4.125, 7.375, 4.25], "texture": "#8"}, "up": {"uv": [5, 5.875, 4.375, 4.125], "texture": "#8"}, "down": {"uv": [5.625, 4.125, 5, 5.875], "texture": "#8"}}}, {"from": [17.5, 8, 0.9], "to": [22.5, 10, 15], "rotation": {"angle": 0, "axis": "y", "origin": [18, 6, 0.5]}, "faces": {"north": {"uv": [0, 0, 0.625, 0.25], "texture": "#9"}, "east": {"uv": [0, 0, 1.7625, 0.25], "texture": "#9"}, "south": {"uv": [0, 0, 0.625, 0.25], "texture": "#9"}, "west": {"uv": [0, 0, 1.7625, 0.25], "texture": "#9"}, "up": {"uv": [0, 0, 0.625, 1.7625], "texture": "#9"}, "down": {"uv": [0, 0, 0.625, 1.7625], "texture": "#9"}}}, {"from": [17.5, 1.5, 0.9], "to": [22.5, 7, 15], "rotation": {"angle": 0, "axis": "y", "origin": [18, 3, 0.5]}, "faces": {"north": {"uv": [0, 0, 0.625, 0.6875], "texture": "#9"}, "east": {"uv": [0, 0, 1.7625, 0.6875], "texture": "#9"}, "south": {"uv": [0, 0, 0.625, 0.6875], "texture": "#9"}, "west": {"uv": [0, 0, 1.7625, 0.6875], "texture": "#9"}, "up": {"uv": [0, 0, 0.625, 1.7625], "texture": "#9"}, "down": {"uv": [0, 0, 0.625, 1.7625], "texture": "#9"}}}, {"from": [-6.5, 1, 15], "to": [16, 11, 16], "rotation": {"angle": 0, "axis": "y", "origin": [8, 0, 0.5]}, "faces": {"north": {"uv": [0, 0, 2.875, 1.25], "texture": "#8"}, "east": {"uv": [2.875, 5.625, 3, 6.875], "texture": "#8"}, "south": {"uv": [0, 1.25, 2.875, 2.5], "texture": "#8"}, "west": {"uv": [3, 5.625, 3.125, 6.875], "texture": "#8"}, "up": {"uv": [8.5, 2, 5.625, 1.875], "texture": "#8"}, "down": {"uv": [8.5, 2, 5.625, 2.125], "texture": "#8"}}}, {"from": [17.5, 1, 15], "to": [22.5, 11, 16], "rotation": {"angle": 0, "axis": "y", "origin": [23, 0, 0.5]}, "faces": {"north": {"uv": [4.75, 0, 5.375, 1.25], "texture": "#8"}, "east": {"uv": [3.125, 5.625, 3.25, 6.875], "texture": "#8"}, "south": {"uv": [0, 5.25, 0.625, 6.5], "texture": "#8"}, "west": {"uv": [3.25, 5.625, 3.375, 6.875], "texture": "#8"}, "up": {"uv": [3.5, 2.25, 2.875, 2.125], "texture": "#8"}, "down": {"uv": [3.5, 2.25, 2.875, 2.375], "texture": "#8"}}}, {"from": [19.75, 8.75, 0.4], "to": [20.25, 9.25, 1], "rotation": {"angle": 0, "axis": "y", "origin": [18.5, 8.5, 0]}, "faces": {"north": {"uv": [1, 2, 2, 3], "texture": "#7"}, "east": {"uv": [2, 1, 3, 2], "texture": "#7"}, "south": {"uv": [2, 2, 3, 3], "texture": "#7"}, "west": {"uv": [0, 3, 1, 4], "texture": "#7"}, "up": {"uv": [4, 1, 3, 0], "texture": "#7"}, "down": {"uv": [2, 3, 1, 4], "texture": "#7"}}}, {"from": [19.75, 8.75, 0.3], "to": [20.25, 9.25, 1], "rotation": {"angle": 45, "axis": "z", "origin": [20, 9, 0.7]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#7"}, "east": {"uv": [0, 1, 1, 2], "texture": "#7"}, "south": {"uv": [1, 0, 2, 1], "texture": "#7"}, "west": {"uv": [1, 1, 2, 2], "texture": "#7"}, "up": {"uv": [1, 3, 0, 2], "texture": "#7"}, "down": {"uv": [3, 0, 2, 1], "texture": "#7"}}}, {"from": [21.75, 3.75, 0.3], "to": [22.25, 4.25, 1], "rotation": {"angle": 45, "axis": "z", "origin": [22, 4, 0.7]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#7"}, "east": {"uv": [0, 1, 1, 2], "texture": "#7"}, "south": {"uv": [1, 0, 2, 1], "texture": "#7"}, "west": {"uv": [1, 1, 2, 2], "texture": "#7"}, "up": {"uv": [1, 3, 0, 2], "texture": "#7"}, "down": {"uv": [3, 0, 2, 1], "texture": "#7"}}}, {"from": [21.75, 3.75, 0.4], "to": [22.25, 4.25, 1], "rotation": {"angle": 0, "axis": "y", "origin": [20.5, 3.5, 0]}, "faces": {"north": {"uv": [1, 2, 2, 3], "texture": "#7"}, "east": {"uv": [2, 1, 3, 2], "texture": "#7"}, "south": {"uv": [2, 2, 3, 3], "texture": "#7"}, "west": {"uv": [0, 3, 1, 4], "texture": "#7"}, "up": {"uv": [4, 1, 3, 0], "texture": "#7"}, "down": {"uv": [2, 3, 1, 4], "texture": "#7"}}}, {"from": [7.75, 11.65, -0.4], "to": [8.25, 12.15, 0.3], "rotation": {"angle": 45, "axis": "z", "origin": [8, 11.9, 0]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#7"}, "east": {"uv": [0, 1, 1, 2], "texture": "#7"}, "south": {"uv": [1, 0, 2, 1], "texture": "#7"}, "west": {"uv": [1, 1, 2, 2], "texture": "#7"}, "up": {"uv": [1, 3, 0, 2], "texture": "#7"}, "down": {"uv": [3, 0, 2, 1], "texture": "#7"}}}, {"from": [7.75, 11.65, -0.3], "to": [8.25, 12.15, 0.3], "rotation": {"angle": 0, "axis": "y", "origin": [6.5, 11.4, -0.7]}, "faces": {"north": {"uv": [1, 2, 2, 3], "texture": "#7"}, "east": {"uv": [2, 1, 3, 2], "texture": "#7"}, "south": {"uv": [2, 2, 3, 3], "texture": "#7"}, "west": {"uv": [0, 3, 1, 4], "texture": "#7"}, "up": {"uv": [4, 1, 3, 0], "texture": "#7"}, "down": {"uv": [2, 3, 1, 4], "texture": "#7"}}}, {"from": [-3.2, 13, 11.6], "to": [-1.2, 14, 15.6], "rotation": {"angle": 22.5, "axis": "y", "origin": [-2.2, 13.5, 13.6]}, "faces": {"north": {"uv": [15.5, 11.5, 15, 4.5], "rotation": 270, "texture": "#2"}, "east": {"uv": [0.5, 11, 15.5, 11.5], "texture": "#2"}, "south": {"uv": [0.5, 4.5, 1, 11.5], "rotation": 270, "texture": "#2"}, "west": {"uv": [15.5, 4.5, 0.5, 5], "texture": "#2"}, "up": {"uv": [0.5, 4.5, 15.5, 11.5], "rotation": 270, "texture": "#2"}, "down": {"uv": [0.5, 4.5, 15.5, 11.5], "rotation": 90, "texture": "#2"}}}, {"from": [1, 13, 12], "to": [3, 14, 16], "rotation": {"angle": -22.5, "axis": "y", "origin": [3, 13, 12]}, "faces": {"north": {"uv": [0, 4, 0.5, 12], "rotation": 270, "texture": "#0"}, "east": {"uv": [16, 4, 0, 4.5], "texture": "#0"}, "south": {"uv": [16, 12, 15.5, 4], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 11.5, 16, 12], "texture": "#0"}, "up": {"uv": [0, 4, 16, 12], "rotation": 90, "texture": "#0"}, "down": {"uv": [0, 4, 16, 12], "rotation": 270, "texture": "#0"}}}, {"from": [-2, 14, 11.6], "to": [2, 15, 13.6], "rotation": {"angle": 22.5, "axis": "y", "origin": [2, 14, 13.6]}, "faces": {"north": {"uv": [0, 11.5, 16, 12], "texture": "#1"}, "east": {"uv": [0, 4, 0.5, 12], "rotation": 270, "texture": "#1"}, "south": {"uv": [16, 4, 0, 4.5], "texture": "#1"}, "west": {"uv": [16, 12, 15.5, 4], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 4, 16, 12], "rotation": 180, "texture": "#1"}, "down": {"uv": [0, 4, 16, 12], "rotation": 180, "texture": "#1"}}}, {"from": [0, 12.9, 1], "to": [16, 13.15, 9], "rotation": {"angle": 0, "axis": "y", "origin": [7, 11.15, 1]}, "faces": {"north": {"uv": [0, 8, 8, 8.25], "texture": "#10"}, "east": {"uv": [8, 0.5, 12, 0.75], "texture": "#10"}, "south": {"uv": [8, 0, 16, 0.25], "texture": "#10"}, "west": {"uv": [8, 1, 12, 1.25], "texture": "#10"}, "up": {"uv": [8, 4, 0, 0], "texture": "#10"}, "down": {"uv": [8, 4, 0, 8], "texture": "#10"}}}, {"from": [15, 13, 10], "to": [17, 15, 12], "rotation": {"angle": 0, "axis": "y", "origin": [15, 13, 10]}, "faces": {"north": {"uv": [0, 0, 2, 2], "texture": "#5"}, "east": {"uv": [0, 2, 2, 4], "texture": "#5"}, "south": {"uv": [2, 0, 4, 2], "texture": "#5"}, "west": {"uv": [2, 2, 4, 4], "texture": "#5"}, "up": {"uv": [2, 6, 0, 4], "texture": "#5"}, "down": {"uv": [6, 0, 4, 2], "texture": "#5"}}}, {"from": [15.5, 15, 9.8], "to": [16.5, 15.5, 10.5], "rotation": {"angle": 0, "axis": "y", "origin": [15, 15, 10]}, "faces": {"north": {"uv": [5, 8, 6, 8.5], "texture": "#5"}, "east": {"uv": [9, 3, 9.5, 3.5], "texture": "#5"}, "south": {"uv": [6, 8, 7, 8.5], "texture": "#5"}, "west": {"uv": [4, 9, 4.5, 9.5], "texture": "#5"}, "up": {"uv": [8, 8.5, 7, 8], "texture": "#5"}, "down": {"uv": [9, 8, 8, 8.5], "texture": "#5"}}}, {"from": [14.8, 15, 11.5], "to": [17.2, 15.5, 12.2], "rotation": {"angle": 0, "axis": "y", "origin": [15, 15, 11.7]}, "faces": {"north": {"uv": [2, 4, 4.5, 4.5], "texture": "#5"}, "east": {"uv": [9, 6, 9.5, 6.5], "texture": "#5"}, "south": {"uv": [4, 2, 6.5, 2.5], "texture": "#5"}, "west": {"uv": [7, 9, 7.5, 9.5], "texture": "#5"}, "up": {"uv": [6.5, 3.5, 4, 3], "texture": "#5"}, "down": {"uv": [4.5, 5, 2, 5.5], "texture": "#5"}}}, {"from": [16.5, 15, 9.8], "to": [17.2, 15.5, 11.5], "rotation": {"angle": 0, "axis": "y", "origin": [16, 15, 10]}, "faces": {"north": {"uv": [9, 5, 9.5, 5.5], "texture": "#5"}, "east": {"uv": [0, 7, 1.5, 7.5], "texture": "#5"}, "south": {"uv": [6, 9, 6.5, 9.5], "texture": "#5"}, "west": {"uv": [2, 7, 3.5, 7.5], "texture": "#5"}, "up": {"uv": [7.5, 3.5, 7, 2], "texture": "#5"}, "down": {"uv": [4.5, 7, 4, 8.5], "texture": "#5"}}}, {"from": [14.8, 15, 9.8], "to": [15.5, 15.5, 11.5], "rotation": {"angle": 0, "axis": "y", "origin": [14.3, 15, 10]}, "faces": {"north": {"uv": [9, 4, 9.5, 4.5], "texture": "#5"}, "east": {"uv": [6, 1, 7.5, 1.5], "texture": "#5"}, "south": {"uv": [5, 9, 5.5, 9.5], "texture": "#5"}, "west": {"uv": [3, 6, 4.5, 6.5], "texture": "#5"}, "up": {"uv": [5.5, 7.5, 5, 6], "texture": "#5"}, "down": {"uv": [6.5, 6, 6, 7.5], "texture": "#5"}}}, {"from": [14.8, 13, 9.8], "to": [15.5, 13.5, 11.5], "rotation": {"angle": 0, "axis": "y", "origin": [14.3, 15, 10]}, "faces": {"north": {"uv": [0, 10, 0.5, 10.5], "texture": "#5"}, "east": {"uv": [8, 1, 9.5, 1.5], "texture": "#5"}, "south": {"uv": [10, 0, 10.5, 0.5], "texture": "#5"}, "west": {"uv": [2, 8, 3.5, 8.5], "texture": "#5"}, "up": {"uv": [8.5, 3.5, 8, 2], "texture": "#5"}, "down": {"uv": [8.5, 4, 8, 5.5], "texture": "#5"}}}, {"from": [15.5, 13, 9.8], "to": [16.5, 13.5, 10.5], "rotation": {"angle": 0, "axis": "y", "origin": [15, 15, 10]}, "faces": {"north": {"uv": [9, 0, 10, 0.5], "texture": "#5"}, "east": {"uv": [9, 8, 9.5, 8.5], "texture": "#5"}, "south": {"uv": [2, 9, 3, 9.5], "texture": "#5"}, "west": {"uv": [9, 9, 9.5, 9.5], "texture": "#5"}, "up": {"uv": [10, 2.5, 9, 2], "texture": "#5"}, "down": {"uv": [4, 9, 3, 9.5], "texture": "#5"}}}, {"from": [16.5, 13, 9.8], "to": [17.2, 13.5, 11.5], "rotation": {"angle": 0, "axis": "y", "origin": [16, 15, 10]}, "faces": {"north": {"uv": [9, 7, 9.5, 7.5], "texture": "#5"}, "east": {"uv": [7, 6, 8.5, 6.5], "texture": "#5"}, "south": {"uv": [8, 9, 8.5, 9.5], "texture": "#5"}, "west": {"uv": [7, 7, 8.5, 7.5], "texture": "#5"}, "up": {"uv": [0.5, 9.5, 0, 8], "texture": "#5"}, "down": {"uv": [1.5, 8, 1, 9.5], "texture": "#5"}}}, {"from": [14.8, 13, 11.5], "to": [17.2, 13.5, 12.2], "rotation": {"angle": 0, "axis": "y", "origin": [15, 15, 11.7]}, "faces": {"north": {"uv": [5, 4, 7.5, 4.5], "texture": "#5"}, "east": {"uv": [1, 10, 1.5, 10.5], "texture": "#5"}, "south": {"uv": [5, 5, 7.5, 5.5], "texture": "#5"}, "west": {"uv": [10, 1, 10.5, 1.5], "texture": "#5"}, "up": {"uv": [2.5, 6.5, 0, 6], "texture": "#5"}, "down": {"uv": [8.5, 0, 6, 0.5], "texture": "#5"}}}, {"from": [14.75, 14.5, 11.5], "to": [17.75, 17.5, 11.5], "rotation": {"angle": -45, "axis": "y", "origin": [16.5, 16, 11.5]}, "faces": {"north": {"uv": [0, 0, 16, 16], "rotation": 270, "texture": "#3"}, "east": {"uv": [0, 0, 3, 0], "rotation": 90, "texture": "#3"}, "south": {"uv": [0, 16, 16, 0], "rotation": 270, "texture": "#3"}, "west": {"uv": [0, 0, 3, 0], "rotation": 270, "texture": "#3"}, "up": {"uv": [0, 0, 3, 0], "texture": "#3"}, "down": {"uv": [0, 0, 3, 0], "rotation": 180, "texture": "#3"}}}], "display": {"thirdperson_righthand": {"scale": [0.3, 0.3, 0.3]}, "thirdperson_lefthand": {"scale": [0.3, 0.3, 0.3]}, "firstperson_righthand": {"scale": [0.3, 0.3, 0.3]}, "firstperson_lefthand": {"scale": [0.3, 0.3, 0.3]}, "ground": {"scale": [0.3, 0.3, 0.3]}, "gui": {"rotation": [0, -23, -28], "scale": [0.3, 0.3, 0.3]}, "head": {"translation": [0, 1.75, 0]}, "fixed": {"rotation": [0, 0, 36], "scale": [0.3, 0.3, 0.3]}}}
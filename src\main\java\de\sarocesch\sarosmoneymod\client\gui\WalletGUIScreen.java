package de.sarocesch.sarosmoneymod.client.gui;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.world.inventory.WalletGUIMenu;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.world.level.Level;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.network.chat.Component;
import java.util.HashMap;
import com.mojang.blaze3d.systems.RenderSystem;

public class WalletGUIScreen extends AbstractContainerScreen<WalletGUIMenu> {
	private final static HashMap<String, Object> guistate = WalletGUIMenu.guistate;
	private final Level world;
	//private final int x, y, z;
	/// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	private final Player entity;

	public WalletGUIScreen(WalletGUIMenu container, Inventory inventory, Component text) {
		super(container, inventory, text);
		this.world = container.world;
		//this.x = container.x;
		//this.y = container.y;
		//this.z = container.z;
		/// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
		this.entity = container.entity;
		this.imageWidth = 176;
		this.imageHeight = 193; // Increased height for 3 rows + 12px adjustment
	}

	private static final ResourceLocation texture = ResourceLocation.fromNamespaceAndPath(SarosMoneyModMod.MODID, "textures/screens/wallet_gui.png");

	@Override
	public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTicks) {
		this.renderBackground(guiGraphics, mouseX, mouseY, partialTicks);
		super.render(guiGraphics, mouseX, mouseY, partialTicks);
		this.renderTooltip(guiGraphics, mouseX, mouseY);
	}

	@Override
	protected void renderBg(GuiGraphics guiGraphics, float partialTicks, int mouseX, int mouseY) {
		RenderSystem.setShaderColor(1, 1, 1, 1);
		RenderSystem.enableBlend();
		RenderSystem.defaultBlendFunc();
		RenderSystem.setShaderTexture(0, texture);
		// Neuer Aufruf: Verwende guiGraphics.blit statt this.blit
		guiGraphics.blit(texture, this.leftPos, this.topPos, 0, 0, this.imageWidth, this.imageHeight, this.imageWidth, this.imageHeight);
		RenderSystem.disableBlend();
	}

	@Override
	public boolean keyPressed(int key, int b, int c) {
		if (key == 256) {
			this.minecraft.player.closeContainer();
			return true;
		}
		return super.keyPressed(key, b, c);
	}

	@Override
	public void containerTick() {
		super.containerTick();
	}

	@Override
	protected void renderLabels(GuiGraphics guiGraphics, int mouseX, int mouseY) {
		// Falls du Text oder Labels rendern möchtest, kannst du das hier tun.
	}

	@Override
	public void onClose() {
		super.onClose();
	}

	@Override
	public void init() {
		super.init();
	}
}
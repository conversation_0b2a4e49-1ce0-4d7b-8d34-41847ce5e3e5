{"item.saros__money_mod.k_5_paycheck": "Credit Card", "item.saros__money_mod.k_2_paycheck": "Credit Card", "command.pay.not_enough_money": "You don't have enough money!", "command.eco.set.success": "Set %2$s's balance to %1$d.", "lang.deposit.new_balance": "New balance: ", "item.saros_projecte_money_mod.k_3_paycheck": "Credit Card", "item.saros__money_mod.euro_20": "20 Dollar", "block.saros__money_mod.atm_2": "ATM", "gui.saros__money_mod.balance": "Balance: %d", "lang.withdraw.not_enough_inventory": "Not enough inventory space!", "item.saros_projecte_money_mod.euro_20": "20 Dollar", "block.saros__money_mod.atm_3": "ATM", "message.death_money_loss": "You lost %s money upon death. Your new balance is %s.", "itemGroup.tabsaros_project_e_money_mod": "Saros Money Mod", "message.sarosmoneymod.paycheck": "You have received your paycheck of %s!", "item.saros_projecte_money_mod.k_10_paycheck": "Credit Card", "gui.saros__money_mod.atmgui.withdraw": "", "item.saros__money_mod.euro_2": "2 Dollar", "gui.saros__money_mod.atmgui.output": "Withdrawn", "item.saros_projecte_money_mod.euro_200": "200 Dollar", "item.saros__money_mod.euro_5": "5 Dollar", "item.saros__money_mod.euro_50": "50 Dollar", "lang.withdraw.enter_valid_number": "Please enter a valid number!", "block.saros__money_mod.atm": "ATM", "item.saros__money_mod.euro_10": "10 Dollar", "command.leaderboard.create.failure": "An error occurred while trying to get the player.", "item.saros_projecte_money_mod.euro_50": "50 Dollar", "item.saros_projecte_money_mod.euro_10": "10 Dollar", "item.saros__money_mod.euro_1": "1 Dollar", "item.saros_projecte_money_mod.euro_5": "5 Dollar", "lang.withdraw.withdrawn_amount": "Withdrawn amount: ", "lang.withdraw.not_enough_balance": "Not enough balance in the account!", "item.saros__money_mod.euro_500": "500 Dollar", "item.saros_projecte_money_mod.euro_2": "2 Dollar", "command.eco.player_not_found": "Player not found.", "item.saros_projecte_money_mod.euro_1": "1 Dollar", "lang.withdraw.previous_balance": "Previous balance: ", "lang.withdraw.account_not_found": "Bank account not found!", "command.pay.success_receiver": "You received %s from %s.", "item.saros__money_mod.k_4_paycheck": "Credit Card", "item.saros_projecte_money_mod.k_4_paycheck": "Credit Card", "item.saros__money_mod.euro_100": "100 Dollar", "gui.saros__money_mod.atmgui.button_payout": "Withdraw", "lang.deposit.added_amount": "Added amount: ", "command.eco.take.success": "Took %1$d from %2$s's account. New balance: %3$d.", "gui.saros__money_mod.atmgui.balance_label": "Balance: $", "lang.deposit.previous_balance": "Previous balance: ", "command.money.not_a_player": "This command can only be run by a player.", "gui.saros__money_mod.atmgui.label_kontostand_value": "Kontostand: <value>", "item.saros_projecte_money_mod.k_5_paycheck": "Credit Card", "command.eco.get.success": "%1$s's balance: %2$d.", "command.leaderboard.create.success": "Leaderboard created!", "item.saros__money_mod.k_3_paycheck": "Credit Card", "itemGroup.tabsaros_money_mod": "Saros Money Mod", "gui.saros__money_mod.atmgui.button_deposit": "<PERSON><PERSON><PERSON><PERSON>", "command.pay.player_not_found": "Target player not found!", "item.saros__money_mod.k_10_paycheck": "Credit Card", "lang.withdraw.enter_positive_number": "Please enter a positive number!", "item.saros_projecte_money_mod.euro_500": "500 Dollar", "item.saros__money_mod.wallet": "Red Wallet", "item.saros__money_mod.wallet_black": "Black Wallet", "item.saros__money_mod.wallet_blue": "Blue Wallet", "item.saros__money_mod.wallet_brown": "<PERSON>", "item.saros__money_mod.wallet_green": "Green Wallet", "item.saros__money_mod.wallet_orange": "Orange Wallet", "item.saros__money_mod.wallet_purple": "Purple Wallet", "item.saros__money_mod.wallet_yellow": "Yellow Wallet", "item.saros_projecte_money_mod.euro_100": "100 Dollar", "command.eco.add.success": "Added %1$d to %2$s's account. New balance: %3$d.", "lang.withdraw.new_balance": "New balance: ", "item.saros__money_mod.k_1_paycheck": "Credit Card", "item.saros_projecte_money_mod.k_2_paycheck": "Credit Card", "command.leaderboard.delete.success": "All leaderboards within 5 blocks have been deleted!", "command.money.balance": "Your current balance is: %s", "item.saros__money_mod.euro_200": "200 Dollar", "command.eco.error": "An error occurred.", "command.leaderboard.delete.failure": "An error occurred while trying to get the player.", "item_group.saros__money_mod.saros_money_mod": "Saros Money Mod", "item.saros_projecte_money_mod.k_1_paycheck": "Credit Card", "command.leaderboard.unknown_player": "Unknown Player", "command.pay.success_sender": "You paid %s to %s.", "entity.minecraft.villager.saros__money_mod.banker": "Banker", "item.saros__money_mod.cent_1": "1 Cent", "item.saros__money_mod.cent_2": "2 Cent", "item.saros__money_mod.cent_5": "5 Cent", "item.saros__money_mod.cent_10": "10 Cent", "item.saros__money_mod.cent_20": "20 Cent", "item.saros__money_mod.cent_50": "50 Cent", "block.saros__money_mod.banker": "Banker Table", "shop.error.no_container": "No container found!", "shop.error.not_enough_money": "You don't have enough money! Required: $%.2f", "shop.error.item_not_available": "The item is no longer available!", "shop.error.inventory_full": "Your inventory is full!", "shop.error.owner_not_enough_money": "The shop owner doesn't have enough money!", "shop.error.item_not_in_inventory": "You don't have this item in your inventory!", "shop.error.container_full": "The container is full!", "shop.error.invalid_shop_type": "Invalid shop type! Use 'buy' or 'sell'", "shop.error.invalid_price_format": "Invalid price format!", "shop.protection.container_owner_only": "Only the owner can remove this!", "shop.protection.sign_owner_only": "Only the owner can remove this sign!", "shop.protection.container_use_owner_only": "Only the owner can use this!", "shop.buy.success": "Buy Shop: You bought %s for $%.2f", "shop.buy.owner_notification": "%s bought %s from your Buy Shop for $%.2f", "shop.sell.success": "Sell Shop: You sold %s for $%.2f", "shop.sell.owner_notification": "%s sold %s to your Sell Shop for $%.2f"}
package de.sarocesch.sarosmoneymod.init;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import net.minecraft.core.registries.Registries;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.world.item.ItemStack;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.neoforged.neoforge.registries.DeferredRegister;

import java.util.function.Supplier;

public class SarosMoneyModModTabs {
	public static final DeferredRegister<CreativeModeTab> REGISTRY =
			DeferredRegister.create(Registries.CREATIVE_MODE_TAB, SarosMoneyModMod.MODID);

	public static final DeferredHolder<CreativeModeTab, CreativeModeTab> SAROS_MONEY_MOD = REGISTRY.register(
			"saros_money_mod",
			() -> CreativeModeTab.builder()
					.title(Component.translatable("item_group.saros__money_mod.saros_money_mod"))
					.icon(() -> new ItemStack(SarosMoneyModModItems.EURO_2.get()))
					.displayItems((parameters, output) -> {
						// Euro Items
						output.accept(SarosMoneyModModItems.EURO_1.get());
						output.accept(SarosMoneyModModItems.EURO_2.get());
						output.accept(SarosMoneyModModItems.EURO_5.get());
						output.accept(SarosMoneyModModItems.EURO_10.get());
						output.accept(SarosMoneyModModItems.EURO_20.get());
						output.accept(SarosMoneyModModItems.EURO_50.get());
						output.accept(SarosMoneyModModItems.EURO_100.get());
						output.accept(SarosMoneyModModItems.EURO_200.get());
						output.accept(SarosMoneyModModItems.EURO_500.get());

						// Cent Items
						output.accept(SarosMoneyModModItems.CENT_1.get());
						output.accept(SarosMoneyModModItems.CENT_2.get());
						output.accept(SarosMoneyModModItems.CENT_5.get());
						output.accept(SarosMoneyModModItems.CENT_10.get());
						output.accept(SarosMoneyModModItems.CENT_20.get());
						output.accept(SarosMoneyModModItems.CENT_50.get());

						// Block Items
						output.accept(SarosMoneyModModBlocks.ATM.get().asItem());
						output.accept(SarosMoneyModModBlocks.ATM_2.get().asItem());
						output.accept(SarosMoneyModModBlocks.ATM_3.get().asItem());
						output.accept(SarosMoneyModModBlocks.BANKER.get().asItem());

						// Wallet Items
						output.accept(SarosMoneyModModItems.WALLET.get());
						output.accept(SarosMoneyModModItems.WALLET_BLACK.get());
						output.accept(SarosMoneyModModItems.WALLET_BLUE.get());
						output.accept(SarosMoneyModModItems.WALLET_BROWN.get());
						output.accept(SarosMoneyModModItems.WALLET_GREEN.get());
						output.accept(SarosMoneyModModItems.WALLET_ORANGE.get());
						output.accept(SarosMoneyModModItems.WALLET_PURPLE.get());
						output.accept(SarosMoneyModModItems.WALLET_YELLOW.get());
					})
					.build()
	);
}
package de.sarocesch.sarosmoneymod.villager;

import com.google.common.collect.ImmutableSet;
import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModBlocks;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.ai.village.poi.PoiType;
import net.minecraft.world.entity.npc.VillagerProfession;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.neoforged.neoforge.registries.DeferredRegister;
import net.neoforged.bus.api.IEventBus;

public class ModVillagers {
    public static final DeferredRegister<PoiType> POI_TYPES =
            DeferredRegister.create(BuiltInRegistries.POINT_OF_INTEREST_TYPE, SarosMoneyModMod.MODID);

    public static final DeferredRegister<VillagerProfession> VILLAGER_PROFESSION =
            DeferredRegister.create(BuiltInRegistries.VILLAGER_PROFESSION, SarosMoneyModMod.MODID);

    // POI-Type Registrierung mit NeoForge 1.21.1 Constructor
    public static final DeferredHolder<PoiType, PoiType> BANK_BLOCK_POI = POI_TYPES.register("bank_block_poi",
            () -> new PoiType(
                    ImmutableSet.copyOf(SarosMoneyModModBlocks.BANKER.get().getStateDefinition().getPossibleStates()),
                    1,  // maxTickets
                    1   // searchDistance
            ));

    // Villager-Profession mit Holder-Referenz
    public static final DeferredHolder<VillagerProfession, VillagerProfession> BANKER =
            VILLAGER_PROFESSION.register("banker",
                    () -> new VillagerProfession(
                            "banker",
                            holder -> holder.value() == BANK_BLOCK_POI.get(),
                            holder -> holder.value() == BANK_BLOCK_POI.get(),
                            ImmutableSet.of(),
                            ImmutableSet.of(),
                            SoundEvents.VILLAGER_WORK_ARMORER
                    ));

    public static void register(IEventBus eventBus) {
        POI_TYPES.register(eventBus);
        VILLAGER_PROFESSION.register(eventBus);
    }
}
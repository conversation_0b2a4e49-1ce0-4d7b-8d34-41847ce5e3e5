package de.sarocesch.sarosmoneymod.command;

import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.neoforge.event.RegisterCommandsEvent;

import java.util.Map;

@EventBusSubscriber(modid = SarosMoneyModMod.MODID)
public class MoneyCommand {

    @SubscribeEvent
    public static void registerCommand(RegisterCommandsEvent event) {
        event.getDispatcher().register(
                Commands.literal("money")
                        .executes(MoneyCommand::showBalance)
        );
    }

    private static int showBalance(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        ServerPlayer player;

        try {
            player = source.getPlayerOrException();
        } catch (CommandSyntaxException e) {
            source.sendFailure(
                    Component.translatable("command.money.not_a_player")
                            .withStyle(ChatFormatting.RED)
            );
            return 0;
        }

        // Load player's balance
        String playerUUID = player.getUUID().toString();
        Map<String, Double> balances = BalanceManager.loadBalances(source.getServer());
        double balance = balances.getOrDefault(playerUUID, 0.0);

        // Send balance message to the player
        player.sendSystemMessage(
                Component.translatable("command.money.balance", String.format("%.2f", balance))
                        .withStyle(ChatFormatting.GREEN)
        );

        return 1;
    }
}
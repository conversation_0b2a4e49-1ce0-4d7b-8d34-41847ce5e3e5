package de.sarocesch.sarosmoneymod.listener;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.data.SignAttachmentType;
import de.sarocesch.sarosmoneymod.data.SignAttachmentType.SignData;
import de.sarocesch.sarosmoneymod.handlers.ServerHandler;
import de.sarocesch.sarosmoneymod.handlers.ShopSignHandler;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.TickTask;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.Container;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.SignBlockEntity;
import net.minecraft.world.level.block.entity.SignText;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.neoforged.bus.api.EventPriority;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.neoforge.event.entity.player.PlayerInteractEvent;
import net.neoforged.neoforge.event.level.BlockEvent;

import java.lang.reflect.Field;
import java.util.UUID;

import static de.sarocesch.sarosmoneymod.SarosMoneyModMod.LOGGER;

public class SignShopListener {

    @SubscribeEvent
    public void onSignPlaced(BlockEvent.EntityPlaceEvent event) {
        if (event.getEntity() instanceof Player player) {
            Level level = (Level) event.getLevel();
            BlockPos signPos = event.getPos();

            if (level.getBlockEntity(signPos) instanceof SignBlockEntity sign) {
                SignData data = SignAttachmentType.getData(sign);
                data.setOwnerUUID(player.getUUID());

                BlockState signState = level.getBlockState(signPos);
                Direction facing = Direction.NORTH;

                if (signState.hasProperty(BlockStateProperties.HORIZONTAL_FACING)) {
                    facing = signState.getValue(BlockStateProperties.HORIZONTAL_FACING);
                } else if (signState.hasProperty(BlockStateProperties.ROTATION_16)) {
                    int rotation = signState.getValue(BlockStateProperties.ROTATION_16);
                    facing = Direction.from2DDataValue(rotation / 4);
                }

                BlockPos containerPos = signPos.relative(facing.getOpposite());

                // Store container position in persistent data
                try {
                    // Use standard Java reflection instead of ReflectionHelper
                    Field persistentDataField = SignBlockEntity.class.getDeclaredField("persistentData");
                    persistentDataField.setAccessible(true);
                    CompoundTag nbt = (CompoundTag) persistentDataField.get(sign);
                    nbt.putIntArray("ContainerPos", new int[]{
                            containerPos.getX(),
                            containerPos.getY(),
                            containerPos.getZ()
                    });
                } catch (NoSuchFieldException e) {
                    // Try alternative field names if "persistentData" doesn't work
                    try {
                        Field persistentDataField = SignBlockEntity.class.getDeclaredField("data");
                        persistentDataField.setAccessible(true);
                        CompoundTag nbt = (CompoundTag) persistentDataField.get(sign);
                        nbt.putIntArray("ContainerPos", new int[]{
                                containerPos.getX(),
                                containerPos.getY(),
                                containerPos.getZ()
                        });
                    } catch (Exception e2) {
                        LOGGER.error("Failed to set container position in sign NBT with both 'persistentData' and 'data' field names", e2);
                    }
                } catch (Exception e) {
                    LOGGER.error("Failed to set container position in sign NBT", e);
                }
                sign.setChanged();

                // Schedule delayed updates after placement
                if (!level.isClientSide() && player instanceof ServerPlayer serverPlayer) {
                    ServerLevel serverLevel = (ServerLevel) level;

                    // Immediate update
                    ServerHandler.handleSignUpdate(serverPlayer, signPos);

                    // Schedule delayed updates
                    serverLevel.getServer().tell(new TickTask(
                            serverLevel.getServer().getTickCount() + 1,
                            () -> ServerHandler.handleSignUpdate(serverPlayer, signPos)
                    ));

                    serverLevel.getServer().tell(new TickTask(
                            serverLevel.getServer().getTickCount() + 5,
                            () -> ServerHandler.handleSignUpdate(serverPlayer, signPos)
                    ));

                    serverLevel.getServer().tell(new TickTask(
                            serverLevel.getServer().getTickCount() + 10,
                            () -> ServerHandler.handleSignUpdate(serverPlayer, signPos)
                    ));
                }
            }
        }
    }

    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onBlockBreak(BlockEvent.BreakEvent event) {
        Player player = event.getPlayer();
        Level level = player.level();
        BlockPos pos = event.getPos();

        if (level.getBlockEntity(pos) instanceof Container) {
            SignBlockEntity sign = findLinkedSign(level, pos);
            if (sign != null) {
                SignData data = SignAttachmentType.getData(sign);
                if (data.getOwnerUUID() != null) {
                    UUID ownerUUID = data.getOwnerUUID();
                    if (!player.getUUID().equals(ownerUUID) && !player.isCreative()) {
                        event.setCanceled(true);
                        player.displayClientMessage(
                                Component.translatable("shop.protection.container_owner_only")
                                        .withStyle(Style.EMPTY.withColor(ChatFormatting.RED).withBold(true)),
                                true
                        );
                    }
                }
            }
        }

        if (level.getBlockEntity(pos) instanceof SignBlockEntity sign) {
            SignData data = SignAttachmentType.getData(sign);
            if (data.getOwnerUUID() != null) {
                UUID ownerUUID = data.getOwnerUUID();
                if (!player.getUUID().equals(ownerUUID) && !player.isCreative()) {
                    event.setCanceled(true);
                    player.displayClientMessage(
                            Component.translatable("shop.protection.sign_owner_only")
                                    .withStyle(Style.EMPTY.withColor(ChatFormatting.RED).withBold(true)),
                            true
                    );
                }
            }
        }
    }

    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onContainerInteract(PlayerInteractEvent.RightClickBlock event) {
        Player player = event.getEntity();
        Level level = event.getLevel();
        BlockPos pos = event.getPos();

        if (level.isClientSide()) return;

        if (level.getBlockEntity(pos) instanceof Container) {
            SignBlockEntity sign = findLinkedSign(level, pos);
            if (sign != null) {
                SignData data = SignAttachmentType.getData(sign);
                if (data.getOwnerUUID() != null) {
                    UUID ownerUUID = data.getOwnerUUID();
                    if (!player.getUUID().equals(ownerUUID) && !player.isCreative()) {
                        boolean isShopInteraction = player.getMainHandItem().isEmpty() &&
                                isShopSign(sign.getText(true).getMessages(true)[0].getString());

                        if (!isShopInteraction) {
                            event.setCanceled(true);
                            player.displayClientMessage(
                                    Component.translatable("shop.protection.container_owner_only")
                                            .withStyle(Style.EMPTY.withColor(ChatFormatting.RED).withBold(true)),
                                    true
                            );
                        }
                    }
                }
            }
        }
    }

    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onPlayerInteract(PlayerInteractEvent.RightClickBlock event) {
        if (event.getLevel().isClientSide()) return;

        BlockPos pos = event.getPos();
        Level level = event.getLevel();
        Player player = event.getEntity();

        if (level.getBlockEntity(pos) instanceof SignBlockEntity sign) {
            Component[] lines = sign.getText(true).getMessages(true);
            String[] signContent = new String[4];
            for (int i = 0; i < 4; i++) {
                signContent[i] = stripFormatting(lines[i]).trim();
            }

            String firstLine = signContent[0].toLowerCase();
            if (!firstLine.matches("\\[?(buy|sell|shop)\\]?")) {
                return;
            }

            event.setCanceled(true);
            SignData data = SignAttachmentType.getData(sign);
            UUID ownerUUID = data.getOwnerUUID();

            try {
                String type = "";
                String itemId = "";
                String priceStr = "";

                if (firstLine.equals("[shop]")) {
                    type = signContent[1].toLowerCase();
                    itemId = signContent[2];
                    priceStr = signContent[3];
                } else {
                    type = firstLine.replaceAll("[\\[\\]]", "").toLowerCase();
                    if (signContent[1].isEmpty()) {
                        itemId = signContent[2];
                        priceStr = signContent[3];
                    } else {
                        itemId = signContent[1];
                        priceStr = signContent[2];
                    }
                }

                if (itemId.isEmpty() || priceStr.isEmpty()) {
                    throw new IllegalArgumentException("Missing item or price");
                }

                double price = Double.parseDouble(
                        priceStr.replaceAll("[^\\d.,]", "")
                                .replace(",", ".")
                );

                ResourceLocation itemKey = ResourceLocation.tryParse(itemId.toLowerCase());
                if (itemKey == null) return;

                Item item = BuiltInRegistries.ITEM.get(itemKey);
                if (item == null) {
                    throw new IllegalArgumentException("Invalid item: " + itemId);
                }

                ShopSignHandler.handleSignInteraction(
                        ownerUUID,
                        player,
                        item.getDescription().getString(),
                        price,
                        type,
                        pos
                );
            } catch (Exception e) {
                LOGGER.error("[Shop Error] Interaction failed: {}", e.getMessage());
                player.sendSystemMessage(
                        Component.literal("Shop Error: " + e.getMessage())
                                .withStyle(ChatFormatting.RED)
                );
            }
        }
    }

    @SubscribeEvent
    public void onSignChanged(BlockEvent.EntityPlaceEvent event) {
        if (event.getEntity() instanceof Player player && event.getLevel() instanceof Level level) {
            BlockPos pos = event.getPos();
            level.getServer().execute(() -> {
                if (level.getBlockEntity(pos) instanceof SignBlockEntity sign &&
                        player instanceof ServerPlayer serverPlayer) {
                    ServerHandler.handleSignUpdate(serverPlayer, pos);
                }
            });
        }
    }

    // ============= HELPER METHODS =============

    private SignBlockEntity findLinkedSign(Level level, BlockPos containerPos) {
        for (BlockPos pos : BlockPos.withinManhattan(containerPos, 5, 5, 5)) {
            if (level.getBlockEntity(pos) instanceof SignBlockEntity sign) {
                try {
                    // Use standard Java reflection instead of ReflectionHelper
                    Field persistentDataField = SignBlockEntity.class.getDeclaredField("persistentData");
                    persistentDataField.setAccessible(true);
                    CompoundTag nbt = (CompoundTag) persistentDataField.get(sign);

                    if (nbt.contains("ContainerPos")) {
                        int[] coords = nbt.getIntArray("ContainerPos");
                        BlockPos linkedPos = new BlockPos(coords[0], coords[1], coords[2]);
                        if (linkedPos.equals(containerPos)) {
                            return sign;
                        }
                    }
                } catch (NoSuchFieldException e) {
                    // Try alternative field names if "persistentData" doesn't work
                    try {
                        Field persistentDataField = SignBlockEntity.class.getDeclaredField("data");
                        persistentDataField.setAccessible(true);
                        CompoundTag nbt = (CompoundTag) persistentDataField.get(sign);

                        if (nbt.contains("ContainerPos")) {
                            int[] coords = nbt.getIntArray("ContainerPos");
                            BlockPos linkedPos = new BlockPos(coords[0], coords[1], coords[2]);
                            if (linkedPos.equals(containerPos)) {
                                return sign;
                            }
                        }
                    } catch (Exception e2) {
                        continue;
                    }
                } catch (Exception e) {
                    continue;
                }
            }
        }
        return null;
    }

    private String stripFormatting(Component component) {
        return component.getString().replaceAll("§[0-9a-fk-or]", "");
    }

    private boolean isShopSign(String firstLine) {
        String line = stripFormatting(Component.literal(firstLine)).trim().toLowerCase();
        return line.contains("[shop]") || line.contains("[buy]") || line.contains("[sell]");
    }
}
package de.sarocesch.sarosmoneymod.init;

import de.sarocesch.sarosmoneymod.client.gui.ATMGUIScreen;
import net.minecraft.client.gui.screens.MenuScreens;
import net.neoforged.api.distmarker.Dist;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.fml.common.Mod;
import net.neoforged.neoforge.client.event.RegisterMenuScreensEvent;

@EventBusSubscriber(bus = EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
public class SarosMoneyModModScreens {
	@SubscribeEvent
	public static void clientLoad(RegisterMenuScreensEvent event) {
		event.register(SarosMoneyModModMenus.ATMGUI.get(), ATMGUIScreen::new);
		//event.register(SarosMoneyModModMenus.WALLET_GUI.get(), WalletGUIScreen::new);
	}
}
//
//package de.sarocesch.sarosmoneymod.item.inventory;
//
//import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
//import de.sarocesch.sarosmoneymod.init.SarosMoneyModModItems;
//import net.minecraftforge.items.ItemStackHandler;
//import net.minecraftforge.fml.common.Mod;
//import net.minecraftforge.eventbus.api.SubscribeEvent;
//import net.minecraftforge.event.entity.item.ItemTossEvent;
//import net.minecraftforge.common.util.LazyOptional;
//import net.minecraftforge.common.capabilities.ICapabilitySerializable;
//import net.minecraftforge.common.capabilities.ForgeCapabilities;
//import net.minecraftforge.common.capabilities.Capability;
//import net.minecraftforge.api.distmarker.OnlyIn;
//import net.minecraftforge.api.distmarker.Dist;
//
//import net.minecraft.world.item.ItemStack;
//import net.minecraft.nbt.CompoundTag;
//import net.minecraft.core.Direction;
//import net.minecraft.client.Minecraft;
//import net.minecraft.core.component.DataComponents;
//import net.minecraft.world.item.component.CustomData;
//
//import de.sarocesch.sarosmoneymod.client.gui.WalletGUIScreen;
//
//import javax.annotation.Nullable;
//import javax.annotation.Nonnull;
//
//@Mod.EventBusSubscriber(Dist.CLIENT)
//public class WalletInventoryCapability implements ICapabilitySerializable<CompoundTag> {
//	private ItemStack walletStack;
//
//	public WalletInventoryCapability() {
//		this(null);
//	}
//
//	public WalletInventoryCapability(ItemStack walletStack) {
//		this.walletStack = walletStack;
//	}
//
//	public void setWalletStack(ItemStack walletStack) {
//		this.walletStack = walletStack;
//	}
//	@SubscribeEvent
//	public static void onItemDropped(ItemTossEvent event) {
//		if (event.getEntity().getItem().getItem() == SarosMoneyModModItems.WALLET.get()) {
//			if (Minecraft.getInstance().screen instanceof WalletGUIScreen) {
//				Minecraft.getInstance().player.closeContainer();
//			}
//		}
//	}
//
//	private final LazyOptional<ItemStackHandler> inventory = LazyOptional.of(this::createItemHandler);
//
//	@Override
//	public <T> LazyOptional<T> getCapability(@Nonnull Capability<T> capability, @Nullable Direction side) {
//		if (capability == ForgeCapabilities.ITEM_HANDLER) {
//			return this.inventory.cast();
//		} else {
//			return LazyOptional.empty();
//		}
//	}
//
//	@Override
//	public CompoundTag serializeNBT() {
//		return getItemHandler().serializeNBT();
//	}
//
//	@Override
//	public void deserializeNBT(CompoundTag nbt) {
//		// Stelle sicher, dass wir einen gültigen NBT-Tag haben
//		if (nbt != null && !nbt.isEmpty()) {
//			getItemHandler().deserializeNBT(nbt);
//
//			// Debug: print the contents of the handler
//			ItemStackHandler handler = getItemHandler();
//			for (int i = 0; i < handler.getSlots(); i++) {
//				ItemStack slotStack = handler.getStackInSlot(i);
//				if (!slotStack.isEmpty()) {
//					SarosMoneyModMod.LOGGER.info("Deserialisiertes Item in Slot " + i + ": " + slotStack.getItem().getClass().getSimpleName() + " x" + slotStack.getCount());
//				}
//			}
//		}
//	}
//
//	private ItemStackHandler createItemHandler() {
//		SarosMoneyModMod.LOGGER.info("WalletInventoryCapability.createItemHandler");
//		return new ItemStackHandler(18) {
//			@Override
//			public int getSlotLimit(int slot) {
//				return 64;
//			}
//
//			@Override
//			public boolean isItemValid(int slot, @Nonnull ItemStack stack) {
//				return stack.getItem() != SarosMoneyModModItems.WALLET.get();
//			}
//
//			@Override
//			public void setSize(int size) {
//				// Do nothing
//			}
//
//			@Override
//			protected void onContentsChanged(int slot) {
//				super.onContentsChanged(slot);
//
//				// Save the inventory to the wallet item
//				if (walletStack != null) {
//					try {
//						// Erstelle einen neuen CompoundTag für die Inventardaten
//						CompoundTag inventoryTag = serializeNBT();
//
//						// Erstelle einen neuen CustomData-Tag
//						CompoundTag nbt = new CompoundTag();
//
//						// Füge die Inventardaten hinzu
//						nbt.put("Inventory", inventoryTag);
//
//						// Erstelle eine neue CustomData-Instanz mit dem aktualisierten Tag
//						CustomData newCustomData = CustomData.of(nbt);
//
//						// Setze die neue CustomData-Instanz im Wallet-ItemStack
//						walletStack.set(DataComponents.CUSTOM_DATA, newCustomData);
//
//						// In 1.20.6 können wir die Daten direkt im vorhandenen ItemStack speichern
//						// Wir müssen keinen neuen ItemStack erstellen und im Inventar ersetzen
//					} catch (Exception e) {
//						e.printStackTrace();
//					}
//				}
//			}
//		};
//	}
//
//	private ItemStackHandler getItemHandler() {
//		ItemStackHandler handler = inventory.orElseThrow(RuntimeException::new);
//		SarosMoneyModMod.LOGGER.info("WalletInventoryCapability.getItemHandler: " + handler);
//		return handler;
//	}
//}

package de.sarocesch.sarosmoneymod.command;

import com.mojang.brigadier.arguments.DoubleArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.SharedSuggestionProvider;
import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.neoforge.event.RegisterCommandsEvent;

@EventBusSubscriber(modid = SarosMoneyModMod.MODID)
public class PayCommand {
    @SubscribeEvent
    public static void registerCommand(RegisterCommandsEvent event) {
        event.getDispatcher().register(
                Commands.literal("pay")
                        .then(Commands.argument("player", StringArgumentType.string())
                                .suggests(PLAYER_NAMES)
                                .then(Commands.argument("amount", DoubleArgumentType.doubleArg(0.01))
                                        .executes(PayCommand::pay)
                                )
                        )
        );
    }

    // Suggestion provider für Spielernamen
    private static final SuggestionProvider<CommandSourceStack> PLAYER_NAMES = (context, builder) ->
            SharedSuggestionProvider.suggest(context.getSource().getOnlinePlayerNames(), builder);

    private static int pay(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        ServerPlayer sender;
        try {
            sender = source.getPlayerOrException();
        } catch (CommandSyntaxException e) {
            source.sendFailure(
                    Component.translatable("command.pay.sender_not_player")
                            .withStyle(ChatFormatting.RED)
            );
            return 0;
        }

        String targetPlayerName = StringArgumentType.getString(context, "player");
        double amount = DoubleArgumentType.getDouble(context, "amount");
        MinecraftServer server = source.getServer();

        // Sender-Balance aus dem neuen System holen
        String senderUUID = sender.getUUID().toString();
        double senderBalance = BalanceManager.getBalance(senderUUID);

        if (senderBalance < amount) {
            sender.sendSystemMessage(
                    Component.translatable("command.pay.not_enough_money")
                            .withStyle(ChatFormatting.RED)
            );
            return 0;
        }

        // Zielspieler suchen
        ServerPlayer targetPlayer = server.getPlayerList().getPlayerByName(targetPlayerName);
        if (targetPlayer == null) {
            sender.sendSystemMessage(
                    Component.translatable("command.pay.player_not_found", targetPlayerName)
                            .withStyle(ChatFormatting.RED)
            );
            return 0;
        }

        // Zielspieler-Balance holen und aktualisieren
        String targetUUID = targetPlayer.getUUID().toString();
        double targetBalance = BalanceManager.getBalance(targetUUID);

        // Balance aktualisieren über BalanceManager
        BalanceManager.setBalance(senderUUID, senderBalance - amount);
        BalanceManager.setBalance(targetUUID, targetBalance + amount);

        // Nachricht an beide Spieler senden
        sender.sendSystemMessage(
                Component.translatable("command.pay.success_sender",
                                String.format("%.2f", amount), targetPlayerName)
                        .withStyle(ChatFormatting.GREEN)
        );
        targetPlayer.sendSystemMessage(
                Component.translatable("command.pay.success_receiver",
                                String.format("%.2f", amount), sender.getName().getString())
                        .withStyle(ChatFormatting.GREEN)
        );

        return 1;
    }
}
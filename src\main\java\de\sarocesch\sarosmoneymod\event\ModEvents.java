package de.sarocesch.sarosmoneymod.event;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModItems;
import de.sarocesch.sarosmoneymod.villager.ModVillagers;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import net.minecraft.world.entity.npc.VillagerProfession;
import net.minecraft.world.entity.npc.VillagerTrades;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.trading.ItemCost;
import net.minecraft.world.item.trading.MerchantOffer;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.neoforge.event.village.VillagerTradesEvent;

import java.util.List;

@EventBusSubscriber(modid = SarosMoneyModMod.MODID)
public class ModEvents {

    @SubscribeEvent
    public static void addCustomTrades(VillagerTradesEvent event) {
        if (!de.sarocesch.sarosmoneymod.Config.VILLAGER_TRADES_ENABLED) {
            return;
        }

        VillagerProfession profession = event.getType();
        if (profession != ModVillagers.BANKER.get()) {
            return;
        }

        Int2ObjectMap<List<VillagerTrades.ItemListing>> trades = event.getTrades();

        // Level 1 Trade
        trades.get(1).add((trader, rand) -> new MerchantOffer(
                new ItemCost(Items.RAW_IRON, de.sarocesch.sarosmoneymod.Config.TRADE_L1_RAW_IRON_AMOUNT),
                new ItemStack(SarosMoneyModModItems.EURO_1.get(), 1),
                10, 8, 0.02F
        ));

        // Level 2 Trade
        trades.get(2).add((trader, rand) -> new MerchantOffer(
                new ItemCost(Items.DIAMOND, de.sarocesch.sarosmoneymod.Config.TRADE_L2_DIAMOND_AMOUNT),
                new ItemStack(SarosMoneyModModItems.EURO_5.get(), 1),
                10, 8, 0.02F
        ));

        // Level 3 Trade
        trades.get(3).add((trader, rand) -> new MerchantOffer(
                new ItemCost(Items.RAW_COPPER, de.sarocesch.sarosmoneymod.Config.TRADE_L3_RAW_COPPER_AMOUNT),
                new ItemStack(SarosMoneyModModItems.EURO_1.get(), 1),
                10, 8, 0.02F
        ));

        // Level 4 Trades
        trades.get(4).add((trader, rand) -> new MerchantOffer(
                new ItemCost(Items.RAW_IRON, de.sarocesch.sarosmoneymod.Config.TRADE_L4_RAW_IRON_AMOUNT),
                new ItemStack(SarosMoneyModModItems.EURO_1.get(), 1),
                10, 8, 0.02F
        ));

        trades.get(4).add((trader, rand) -> new MerchantOffer(
                new ItemCost(Items.DIAMOND, de.sarocesch.sarosmoneymod.Config.TRADE_L4_DIAMOND_AMOUNT),
                new ItemStack(SarosMoneyModModItems.EURO_5.get(), 1),
                10, 8, 0.02F
        ));

        // Level 5 Trades
        trades.get(5).add((trader, rand) -> new MerchantOffer(
                new ItemCost(Items.ENCHANTED_GOLDEN_APPLE, de.sarocesch.sarosmoneymod.Config.TRADE_L5_GOLDEN_APPLE_AMOUNT),
                new ItemStack(SarosMoneyModModItems.EURO_500.get(), 1),
                10, 8, 0.02F
        ));

        trades.get(5).add((trader, rand) -> new MerchantOffer(
                new ItemCost(Items.NETHER_STAR, de.sarocesch.sarosmoneymod.Config.TRADE_L5_NETHER_STAR_AMOUNT),
                new ItemStack(SarosMoneyModModItems.EURO_200.get(), 1),
                10, 8, 0.02F
        ));
    }
}
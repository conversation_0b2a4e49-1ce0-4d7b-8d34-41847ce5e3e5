{"command.pay.not_enough_money": "", "item.saros__money_mod.k_5_paycheck": "<PERSON><PERSON>", "item.saros__money_mod.k_2_paycheck": "<PERSON><PERSON>", "command.eco.set.success": "Ustawiono stan konta %2$s na %1$d.", "lang.deposit.new_balance": "<PERSON>e saldo: ", "item.saros_projecte_money_mod.k_3_paycheck": "<PERSON><PERSON>", "item.saros__money_mod.euro_20": "20 Dolarów", "block.saros__money_mod.atm_2": "ATM", "gui.saros__money_mod.balance": "Bilans: %d", "lang.withdraw.not_enough_inventory": "Niewystarczająco miejsca w ekwipunku!", "item.saros_projecte_money_mod.euro_20": "20 Dolarów", "block.saros__money_mod.atm_3": "ATM", "message.death_money_loss": "You lost %s money upon death. Your new balance is %s.", "itemGroup.tabsaros_project_e_money_mod": "Saros Money Mod", "message.sarosmoneymod.paycheck": "You have received your paycheck of %s!", "item.saros_projecte_money_mod.k_10_paycheck": "<PERSON><PERSON>", "gui.saros__money_mod.atmgui.withdraw": "Wycofaj", "item.saros__money_mod.euro_2": "2 Dolarów", "gui.saros__money_mod.atmgui.output": "Wycofaj", "item.saros_projecte_money_mod.euro_200": "200 Dolarów", "item.saros__money_mod.euro_5": "5 Dolarów", "item.saros__money_mod.euro_50": "50 Dolarów", "lang.withdraw.enter_valid_number": "Proszę wprowadzić poprawny numer!", "block.saros__money_mod.atm": "ATM", "item.saros__money_mod.euro_10": "10 Dolarów", "command.leaderboard.create.failure": "An error occurred while trying to get the player.", "item.saros_projecte_money_mod.euro_50": "50 Dolarów", "item.saros__money_mod.euro_1": "1 Dolar", "item.saros_projecte_money_mod.euro_10": "10 Dolarów", "item.saros_projecte_money_mod.euro_5": "5 Dolarów", "lang.withdraw.not_enough_balance": "Za mało środków na koncie!", "lang.withdraw.withdrawn_amount": "Dodana kwota: ", "item.saros__money_mod.euro_500": "500 Dolarów", "item.saros_projecte_money_mod.euro_2": "2 Dolarów", "command.eco.player_not_found": "Nie znaleziono gracza.", "item.saros_projecte_money_mod.euro_1": "1 Dolar", "lang.withdraw.previous_balance": "Poprzednie saldo: ", "lang.withdraw.account_not_found": "Nie znaleziono konta bankowego!", "command.pay.success_receiver": "", "item.saros__money_mod.k_4_paycheck": "<PERSON><PERSON>", "item.saros_projecte_money_mod.k_4_paycheck": "<PERSON><PERSON>", "item.saros__money_mod.euro_100": "100 Dolarów", "gui.saros__money_mod.atmgui.button_payout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lang.deposit.added_amount": "Dodana kwota: ", "command.eco.take.success": "Zabrano %1$d z konta %2$s. Nowy stan konta: %3$d.", "gui.saros__money_mod.atmgui.balance_label": "<PERSON>: $", "lang.deposit.previous_balance": "Poprzednie saldo: ", "command.money.not_a_player": "", "gui.saros__money_mod.atmgui.label_kontostand_value": "Kontostand: <value>", "item.saros_projecte_money_mod.k_5_paycheck": "<PERSON><PERSON>", "command.eco.get.success": "Stan konta %1$s: %2$d.", "command.leaderboard.create.success": "Leaderboard created!", "item.saros__money_mod.k_3_paycheck": "<PERSON><PERSON>", "itemGroup.tabsaros_money_mod": "Saros Money Mod", "gui.saros__money_mod.atmgui.button_deposit": "<PERSON><PERSON><PERSON><PERSON>", "command.pay.player_not_found": "Target player not found!", "item.saros__money_mod.k_10_paycheck": "<PERSON><PERSON>", "lang.withdraw.enter_positive_number": "Proszę wprowadzić liczbę dodatnią!", "item.saros_projecte_money_mod.euro_500": "500 Dolarów", "item.saros__money_mod.wallet": "Portfel", "item.saros_projecte_money_mod.euro_100": "100 Dolarów", "command.eco.add.success": "Dodano %1$d do konta %2$s. Nowy stan konta: %3$d.", "lang.withdraw.new_balance": "<PERSON>e saldo: ", "item.saros__money_mod.k_1_paycheck": "<PERSON><PERSON>", "item.saros_projecte_money_mod.k_2_paycheck": "<PERSON><PERSON>", "command.leaderboard.delete.success": "All leaderboards within 5 blocks have been deleted!", "command.money.balance": "", "command.eco.error": "W<PERSON><PERSON><PERSON><PERSON>ł błąd.", "item.saros__money_mod.euro_200": "200 Dolarów", "command.leaderboard.delete.failure": "An error occurred while trying to get the player.", "item_group.saros__money_mod.saros_money_mod": "Saros Money Mod", "item.saros_projecte_money_mod.k_1_paycheck": "<PERSON><PERSON>", "command.leaderboard.unknown_player": "Unknown Player", "command.pay.success_sender": ""}
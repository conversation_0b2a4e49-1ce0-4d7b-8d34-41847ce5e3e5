package de.sarocesch.sarosmoneymod.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.neoforge.event.RegisterCommandsEvent;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@EventBusSubscriber(modid = SarosMoneyModMod.MODID)
public class EcoCommand {
    @SubscribeEvent
    public static void registerCommand(RegisterCommandsEvent event) {
        CommandDispatcher<CommandSourceStack> dispatcher = event.getDispatcher();

        dispatcher.register(Commands.literal("eco")
                .then(Commands.literal("add")
                        .requires(source -> source.hasPermission(2))
                        .then(Commands.argument("player", StringArgumentType.word())
                                .suggests((context, builder) -> net.minecraft.commands.SharedSuggestionProvider.suggest(getOnlinePlayerNames(context), builder))
                                .then(Commands.argument("amount", IntegerArgumentType.integer())
                                        .executes(context -> addBalance(context)))))
                .then(Commands.literal("set")
                        .requires(source -> source.hasPermission(2))
                        .then(Commands.argument("player", StringArgumentType.word())
                                .suggests((context, builder) -> net.minecraft.commands.SharedSuggestionProvider.suggest(getOnlinePlayerNames(context), builder))
                                .then(Commands.argument("amount", IntegerArgumentType.integer())
                                        .executes(context -> setBalance(context)))))
                .then(Commands.literal("take")
                        .requires(source -> source.hasPermission(2))
                        .then(Commands.argument("player", StringArgumentType.word())
                                .suggests((context, builder) -> net.minecraft.commands.SharedSuggestionProvider.suggest(getOnlinePlayerNames(context), builder))
                                .then(Commands.argument("amount", IntegerArgumentType.integer())
                                        .executes(context -> takeBalance(context)))))
                .then(Commands.literal("get")
                        .requires(source -> source.hasPermission(2))
                        .then(Commands.argument("player", StringArgumentType.word())
                                .suggests((context, builder) -> net.minecraft.commands.SharedSuggestionProvider.suggest(getOnlinePlayerNames(context), builder))
                                .executes(context -> getBalance(context)))));
    }

    private static int addBalance(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        String playerName = StringArgumentType.getString(context, "player");
        int amount = IntegerArgumentType.getInteger(context, "amount");
        ServerPlayer player = context.getSource().getServer().getPlayerList().getPlayerByName(playerName);

        if (player != null) {
            UUID playerUUID = player.getUUID();
            int currentBalance = (int) BalanceManager.getPlayerBalance(player);
            int newBalance = currentBalance + amount;
            BalanceManager.updatePlayerBalance(player, newBalance);
            context.getSource().sendSuccess(
                    () -> Component.translatable("command.eco.add.success", amount, playerName, newBalance).withStyle(ChatFormatting.GREEN),
                    false); // Broadcast nicht mehr unterstützt
            return 1;
        } else {
            context.getSource().sendFailure(
                    Component.translatable("command.eco.player_not_found").withStyle(ChatFormatting.RED));
            return 0;
        }
    }

    private static int setBalance(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        String playerName = StringArgumentType.getString(context, "player");
        int amount = IntegerArgumentType.getInteger(context, "amount");
        ServerPlayer player = context.getSource().getServer().getPlayerList().getPlayerByName(playerName);

        if (player != null) {
            BalanceManager.updatePlayerBalance(player, amount);
            context.getSource().sendSuccess(
                    () -> Component.translatable("command.eco.set.success", amount, playerName).withStyle(ChatFormatting.GREEN),
                    false); // Broadcast nicht mehr unterstützt
            return 1;
        } else {
            context.getSource().sendFailure(
                    Component.translatable("command.eco.player_not_found").withStyle(ChatFormatting.RED));
            return 0;
        }
    }

    private static int takeBalance(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        String playerName = StringArgumentType.getString(context, "player");
        int amount = IntegerArgumentType.getInteger(context, "amount");
        ServerPlayer player = context.getSource().getServer().getPlayerList().getPlayerByName(playerName);

        if (player != null) {
            int currentBalance = (int) BalanceManager.getPlayerBalance(player);
            int newBalance = currentBalance - amount;
            BalanceManager.updatePlayerBalance(player, newBalance);
            context.getSource().sendSuccess(
                    () -> Component.translatable("command.eco.take.success", amount, playerName, newBalance).withStyle(ChatFormatting.GREEN),
                    false); // Broadcast nicht mehr unterstützt
            return 1;
        } else {
            context.getSource().sendFailure(
                    Component.translatable("command.eco.player_not_found").withStyle(ChatFormatting.RED));
            return 0;
        }
    }

    private static int getBalance(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        String playerName = StringArgumentType.getString(context, "player");
        ServerPlayer player = context.getSource().getServer().getPlayerList().getPlayerByName(playerName);

        if (player != null) {
            int currentBalance = (int) BalanceManager.getPlayerBalance(player);
            context.getSource().sendSuccess(
                    () -> Component.translatable("command.eco.get.success", playerName, currentBalance).withStyle(ChatFormatting.GREEN),
                    false); // Broadcast nicht mehr unterstützt
            return 1;
        } else {
            context.getSource().sendFailure(
                    Component.translatable("command.eco.player_not_found").withStyle(ChatFormatting.RED));
            return 0;
        }
    }

    private static List<String> getOnlinePlayerNames(CommandContext<CommandSourceStack> context) {
        return context.getSource().getServer().getPlayerList().getPlayers().stream()
                .map(ServerPlayer::getGameProfile)
                .map(profile -> profile.getName())
                .collect(Collectors.toList());
    }
}